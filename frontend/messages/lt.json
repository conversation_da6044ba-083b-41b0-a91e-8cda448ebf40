{"common": {"loading": "<PERSON><PERSON><PERSON><PERSON>...", "error": "Įvyko klaida", "success": "Sėkmė", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Red<PERSON><PERSON><PERSON>", "back": "Atgal", "next": "Toliau", "previous": "<PERSON><PERSON><PERSON><PERSON>", "close": "Uždaryti"}, "navigation": {"home": "Pradžia", "findTerminals": "<PERSON><PERSON><PERSON>", "features": "Funkcijos", "pricing": "<PERSON><PERSON>", "docs": "Dokumentacija", "contact": "Kontaktai", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON> skydas", "logout": "<PERSON>si<PERSON><PERSON><PERSON>"}, "docs": {"title": "API Dokumentacija", "subtitle": "Interaktyvi Pašto Terminalų API dokumentacija", "apiKey": {"title": "API Raktas", "placeholder": "Įveskite savo API raktą norint išbandyti galutinių taškų", "required": "API raktas reikalingas šiam galutiniam taškui išbandyti", "invalid": "Neteisingas API rakto formatas", "hideKey": "Slėpti API raktą", "showKey": "Rodyti API raktą", "format": "Formatas: ptapi_[64-simbolių-hex-eilutė]"}, "endpoints": {"terminals": "Terminalų Galutiniai Taškai", "tracking": "Siuntų Sekimas"}, "tryItOut": "Išbandyti", "execute": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "copied": "Nukopijuota!", "request": "Užklausa", "response": "Atsakas", "parameters": "Parametrai", "requestBody": "<PERSON>ž<PERSON><PERSON><PERSON>", "responses": "Atsakai", "example": "Pa<PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Tipas", "authentication": "Autentifikacija", "rateLimit": "Užklausų Ribojimas", "caching": "Talpin<PERSON>s", "errors": "Klaidų Atsakai", "loading": "<PERSON><PERSON><PERSON><PERSON>...", "error": "Įvyko klaida v<PERSON>dant užklausą", "noResponse": "Atsakas negautas", "curlCommand": "cURL Komanda", "requestUrl": "Užklausos URL", "statusCode": "<PERSON><PERSON><PERSON><PERSON>", "responseTime": "<PERSON><PERSON><PERSON>", "responseHeaders": "<PERSON><PERSON><PERSON>", "responseBody": "<PERSON><PERSON><PERSON>", "url": "URL", "curl": "cURL", "path": "<PERSON><PERSON><PERSON>", "query": "Užklausa", "header": "Antraštė", "gettingStarted": {"title": "Pradžia", "quickStartGuide": "<PERSON><PERSON><PERSON>", "startBuilding": "<PERSON><PERSON><PERSON><PERSON>", "viewExamples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalEndpoints": "<PERSON><PERSON> Galutini<PERSON> Taškų", "restfulEndpoints": "RESTful API galutiniai taškai", "rateLimit": "Užklausų Ribojimas", "requestsPerMinute": "užklausų per minutę", "uptime": "<PERSON><PERSON><PERSON><PERSON>", "serviceAvailability": "pas<PERSON><PERSON> p<PERSON>", "getApiKey": "Gauti API Raktą", "makeRequest": "Atlikti Užklausą", "handleResponse": "Apdoroti Atsaką"}, "authSection": {"apiKeyRequired": "API Raktas Reikalingas", "apiKeyDescription": "Visi galutiniai taškai reikalauja API rakto X-API-Key antraštėje. Formatas: ptapi_[64-simbolių-hex-eilutė]", "rateLimits": "Užklausų Ribojimai", "rateLimitsDescription": "1,000 užklausų per minutę vienam API raktui. Užklausų ribojimo antraštės įtrauktos į atsakus.", "caching": "Talpin<PERSON>s", "cachingDescription": "Terminalų duomenys ta<PERSON> 5 minutes, p<PERSON><PERSON>škos rezultatai 2 minutes <PERSON><PERSON> na<PERSON>."}, "terminals": {"overview": "Terminalų API Apžvalga", "overviewDescription": "Prieiga prie išsamių pašto terminalų duomenų, įskaitant vietas, paslaugas ir darbo valandas.", "availableEndpoints": "Prieinami Galutiniai Taškai", "terminalOperations": "Terminalų operacijos", "responseFormat": "Atsako Formatas", "structuredData": "Struktū<PERSON>uoti duomenys", "cacheDuration": "<PERSON><PERSON><PERSON><PERSON>", "optimizedPerformance": "Optimizu<PERSON><PERSON>"}, "tracking": {"features": "Siuntų Sekimo Funkcijos", "featuresDescription": "Realaus laiko sekimo informacija siuntoms ir krovinių gabenimui pa<PERSON><PERSON>.", "availableEndpoints": "Prieinami Galutiniai Taškai", "trackingOperations": "Sekimo operacijos", "updateFrequency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realTime": "Realaus laiko", "liveTrackingData": "Gyvų sekimo duomenų", "coverage": "<PERSON><PERSON><PERSON><PERSON>", "global": "Globalus", "worldwideTracking": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>"}, "errorHandling": {"title": "<PERSON><PERSON><PERSON>", "description": "Visi galutiniai taškai grąžina klaidas nuosekliu formatu:", "commonStatusCodes": "Dažni HTTP Būsenos Ko<PERSON>:", "success": "Sėkmė", "badRequest": "Bloga Užklausa", "unauthorized": "Neautorizuotas", "notFound": "<PERSON>era<PERSON>", "tooManyRequests": "Per <PERSON><PERSON>", "internalServerError": "<PERSON><PERSON><PERSON><PERSON>"}, "codeExamples": {"title": "<PERSON><PERSON>", "javascript": "JavaScript/Node.js", "python": "Python"}, "sidebar": {"apiDocumentation": "API Dokumentacija", "postalTerminalApi": "Pašto Terminalų API v1.0", "endpoints": "Galutiniai Taškai", "rateLimit": "Užklausų Ribojimas"}, "navigation": {"gettingStarted": "Pradžia", "authentication": "Autentifikacija", "terminalEndpoints": "Terminalų Galutiniai Taškai", "trackingEndpoints": "Sekimo Galutiniai Taškai", "errorHandling": "<PERSON><PERSON><PERSON>", "codeExamples": "<PERSON><PERSON>"}}, "landing": {"hero": {"badge": "Suvienyta Siuntų Spintų API", "title": "Raskite Pašto Terminalus Visoje Lietuvoje", "subtitle": "Prieiga prie 1,923+ pašto terminalų iš LP Express, Omniva, DPD ir Venipak su mūsų išsamia API.", "searchTitle": "", "searchPlaceholder": "Ieškokite pagal miestą, ad<PERSON>ą ar terminalo pavadinimą...", "searchButton": "Ieškoti", "realTimeData": "Realaus Laiko Duomenys", "packageTracking": "Siuntų Sekimas", "fastApi": "Greita API", "cta": "<PERSON><PERSON><PERSON><PERSON>", "viewDocs": "Žiūrėti API Dokumentaciją"}, "features": {"title": "Kodėl Rinktis Mūsų API?", "subtitle": "<PERSON><PERSON><PERSON>, suprojektuota plėtrai", "realtime": {"title": "Realaus Laiko Duomenys", "description": "Gaukite naujausius pašto terminalų duomenis su nuolat atnaujinamomis duomenų bazėmis"}, "reliable": {"title": "99.9% Veikim<PERSON>", "description": "Įmonės lygio infrastruktūra <PERSON>, kad jūsų programos veiktų be pertrūkių"}, "fast": {"title": "Žaibiškai Greita", "description": "Optimizuoti galutiniai taškai atsako per mažiau nei 100ms visame pasaulyje"}, "secure": {"title": "<PERSON><PERSON><PERSON> ir <PERSON>", "description": "SOC 2 atitinkanti su įmonės lygio saugumu ir duomenų apsauga"}}, "pricing": {"title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "subtitle": "Pasirinkite tobulą planą savo poreikiams. Visi planai apima prieigą prie mūsų išsamios pašto terminalų duomenų bazės.", "monthly": "M<PERSON><PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON><PERSON>", "save": "Sutaupykite 20%", "starter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "period": "/mėn", "description": "Tobulas mažoms programoms ir testavimui", "features": ["10,000 API iškvietimų/mėn", "Visi pašto terminalų duomenys", "Bazinė pagalba", "Standartiniai užklausų ribojimai"], "cta": "<PERSON><PERSON><PERSON><PERSON>"}, "pro": {"name": "Profesionalus", "description": "Geriausias augančioms įmonėms ir programoms", "period": "/mėn", "features": ["100,000 API iškvietimų/mėn", "Realaus laiko terminalų duomenys", "Pirmeny<PERSON><PERSON><PERSON> p<PERSON>", "Iš<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Webhook pranešimai", "Pritaikytos integracijos"], "cta": "Pradėti Pro Planą", "popular": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "cta": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "subtitle": "Prisijunkite prie tūkstančių kūrėjų, naudojančių mūsų API savo programoms", "button": "Pradėti Nemokamą Bandomąjį Laikotarpį"}}, "auth": {"login": {"title": "Sveiki Sugrįžę", "subtitle": "Prisijunkite prie savo paskyros, kad tęst<PERSON>", "email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "emailPlaceholder": "Įveskite savo el. pa<PERSON><PERSON> ad<PERSON>", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordPlaceholder": "Įveskite savo slaptažodį", "rememberMe": "<PERSON><PERSON><PERSON><PERSON><PERSON> mane", "forgotPassword": "Pamiršote slaptažodį?", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "or": "arba", "noAccount": "Neturite paskyros?", "signUp": "Registruokit<PERSON><PERSON>", "googleSignIn": "Tęsti su <PERSON>", "continueWithGoogle": "Tęsti su <PERSON>", "signingIn": "Prisijungiama..."}, "register": {"title": "Sukurti paskyrą", "subtitle": "Valdykite atsiskaitymus ir <PERSON> r<PERSON>", "firstName": "Vardas", "firstNamePlaceholder": "Įveskite savo vardą", "lastName": "Pa<PERSON><PERSON>", "lastNamePlaceholder": "Įveskite savo pavardę", "email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "emailPlaceholder": "Įveskite savo el. pa<PERSON><PERSON> ad<PERSON>", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordPlaceholder": "Įveskite savo slaptažodį", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "confirmPasswordPlaceholder": "Patvirtinkite savo slaptažodį", "acceptTerms": "<PERSON><PERSON><PERSON> su", "termsLink": "Paslaugų teikimo sąlygomis", "and": "ir", "privacyLink": "Privatumo politika", "submit": "Sukurti paskyrą", "or": "arba", "hasAccount": "Jau turite paskyrą?", "signIn": "Prisijunkite čia", "googleSignUp": "Tęsti su <PERSON>"}, "forgotPassword": {"title": "Atkurkite Savo Slaptažodį", "subtitle": "Įveskite savo el. pa<PERSON><PERSON> ad<PERSON> ir mes atsiųsime atkūrimo nuorodą", "email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "submit": "Siųsti atkūrimo nuorodą", "backToLogin": "Grįžti prie prisijungimo", "success": "Atkūrimo nuoroda išsiųsta! Patikrinkite savo el. paštą dėl instrukcijų."}, "resetPassword": {"title": "Nustatyti naują slaptažodį", "subtitle": "Įveskite savo naują slaptažodį žemiau", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> slaptažodį", "submit": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaptažodį", "success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>mingai atnaujintas!"}, "passwordStrength": {"label": "Slaptažod<PERSON><PERSON> stiprumas", "veryWeak": "Labai silpnas", "weak": "<PERSON><PERSON><PERSON><PERSON>", "fair": "<PERSON><PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "strong": "St<PERSON><PERSON>"}, "validation": {"invalidEmail": "Neteisingas el. pašto adresas", "passwordMinLength": "Slap<PERSON>ž<PERSON><PERSON> turi b<PERSON>ti bent 6 simbolių", "passwordMinLengthRegister": "Slap<PERSON>žod<PERSON> turi būti bent 8 simbolių", "passwordComplexity": "Slaptažodis turi turėti bent vieną didžiąją raidę, ma<PERSON><PERSON><PERSON><PERSON> raidę ir skaičių", "firstNameMinLength": "Vardas turi būti bent 2 simbolių", "lastNameMinLength": "Pavardė turi būti bent 2 simbolių", "passwordsNoMatch": "Slaptažodžiai nesutampa", "acceptTerms": "Turite sutikti su sąlygomis ir nuostatomis"}, "emailVerification": {"title": "Patvirtinkite savo el. paštą", "message": "<PERSON>s i<PERSON><PERSON><PERSON>ėme patvirtinimo nuorodą į jūsų el. pašto ad<PERSON>. Prašome patikrinti savo pašto dėžutę ir spustelėti nuorodą, kad patvirtintum<PERSON>te savo paskyrą.", "emailSentTo": "<PERSON><PERSON> la<PERSON> i<PERSON> į:", "resendEmail": "Siųsti patvirtinimo el. la<PERSON>ą iš naujo", "resendIn": "Siųsti iš naujo po", "backToLogin": "Grįžti prie prisijungimo", "verifyingTitle": "Tikrinamas el. paštas...", "verifyingMessage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, kol patvirtinsime jū<PERSON>ų el. paš<PERSON> ad<PERSON>.", "successTitle": "El. pa<PERSON>tas pat<PERSON>!", "successMessage": "Jūsų el. paštas sėkmingai patvirtintas. Dabar galite naudotis visomis savo paskyros funk<PERSON>.", "continueToLogin": "Tęsti prie prisijungimo", "helpTitle": "Negavote el. laiško?", "helpStep1": "Patikrinkite savo šlamšto ar nepageidaujamų laiškų aplanką", "helpStep2": "Įsitikinkite, kad el. pa<PERSON><PERSON> ad<PERSON> te<PERSON>", "helpStep3": "Palau<PERSON>te kelias minutes ir bandykite dar kartą", "stillNeedHelp": "Vis dar reikia paga<PERSON>?", "contactSupport": "Susisiekti su palaikymu"}, "verifyEmail": {"title": "Patvirtinkite savo el. paštą", "subtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pat<PERSON> nuo<PERSON> į jūsų el. pa<PERSON>to ad<PERSON>", "resend": "Siųsti patvirtinimo el. la<PERSON>ą iš naujo", "success": "El. paš<PERSON> sėkmingai patvirtintas!", "error": "Netinkama arba pasibaigusi patvirtin<PERSON> nuoroda"}, "oauth": {"processingGoogleAuth": "Apdorojama Google autentifikacija...", "authenticationFailed": "Autentifikacija nepavyko", "authenticationSuccessful": "Autentifikacija sėkminga", "googleAuthError": "Google autentifikacija nepavyko. Bandykite dar kartą.", "backToLogin": "Grįžti į prisijungimą", "redirectingToDashboard": "Nukreipiama į valdymo skydą..."}, "errors": {"loginFailed": "Neteisingas el. paštas arba slaptažodis", "registrationFailed": "Registracija nepavyko. Bandykite dar kartą."}}, "profile": {"deleteAccount": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "deleteAccountWarning": "Įspėjimas: <PERSON><PERSON> ve<PERSON> negal<PERSON>", "deleteAccountDescription": "Tai visam laikui ištrins jūsų paskyrą ir pašalins visus jūsų duomenis iš mūsų serverių.", "deleteAccountConfirmation": "Šio veiksmo negalima atšaukti. Tai visam laikui ištrins jūsų paskyrą ir pašalins visus jūsų duomenis iš mūsų serverių.", "typeDeleteToConfirm": "Įveskite DELETE patvirtinimui", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Trinama...", "dangerZone": "Pavojaus zona"}, "errors": {"required": "<PERSON><PERSON> la<PERSON> yra privalomas", "email": "Įveskite galiojantį el. pa<PERSON><PERSON> ad<PERSON>", "password": "Slap<PERSON>žod<PERSON> turi būti bent 8 simbolių", "passwordMatch": "Slaptažodžiai nesutampa", "terms": "Turite sutikti su sąlygomis ir nuostatomis", "loginFailed": "Neteisingas el. paštas arba slaptažodis", "networkError": "Tinklo klaida. Bandykite dar kartą.", "serverError": "Serverio klaida. Bandykite vėliau.", "resetFailed": "Nepavyko atkurti slaptažodžio. Bandykite dar kartą.", "invalidToken": "Neteisingas arba pasibaigęs žetonas. Prašykite naujo slaptažodžio atkūrimo.", "passwordTooWeak": "Slap<PERSON>žodis per silpnas. Pasirinkite stipresnį slaptažodį.", "verificationFailed": "El. pa<PERSON><PERSON> patvir<PERSON>. Bandykite dar kartą arba prašykite naujo patvirtinimo el. la<PERSON>.", "resendFailed": "Nepavyko išsiųsti patvirtinimo el. laiško. Bandykite vėliau."}, "ui": {"geolocationNotSupported": "Geolokacija nepalaikoma šioje naršyklėje.", "locationFound": "Jūsų vieta rasta! Ieškoma artimų terminalų...", "locationError": "Nepavyko nustatyti jūsų vietos. Bandykite dar kartą arba ieškokite rankiniu būdu.", "allProviders": "Visi teikėjai", "about": "Apie mus", "privacyPolicy": "Privatumo politika", "termsOfService": "Paslaugų teikimo sąlygos", "checking": "Tikrinama...", "allPlansInclude": "Visi planai apima:", "lpExpress": "LP Express", "omniva": "Omniva", "dpd": "DPD", "venipak": "Venipak", "search": "Ieškoti", "go": "Eiti", "readyToStart": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "choosePlan": "Pasirinkite tobulą planą savo poreikiams ir pradėkite naudoti pašto terminalų duomenis <PERSON>n", "startBuilding": "Pradėkite kurti su mūsų API", "getStarted": "<PERSON><PERSON><PERSON><PERSON>", "viewDocumentation": "Žiūrėti dokumentaciją", "docs": "Dokumentacija", "fastApiResponse": "Greitas API atsakas", "developerSupport": "Kūrė<PERSON><PERSON> palaikym<PERSON>", "secureReliable": "<PERSON><PERSON><PERSON> ir pat<PERSON>", "realTimeData": "Realaus laiko duomenys", "accessUpToDate": "Gaukite naujausius terminalų duomenis su gyvais būsenos atnaujinimais", "packageTracking": "Siuntų sekimas", "trackPackages": "Sekite siuntas visuose pagrindinuose pa<PERSON>to te<PERSON> vienoje vietoje", "lightningFast": "Žaibiškai greitas", "optimizedApi": "Optimizuoti API atsakymai pateikiami per mažiau nei 100ms visame pasaulyje", "easyTerminalSearch": "Lengvas terminalų paieška", "easilySearch": "Lengvai ieškokite terminalų keliuose teikėjuose naudodami terminalo pavadinimą, miestą, pa<PERSON>to kodą ar vietą", "startBuildingToday": "Pradėkite kurti su mūsų API šiandien", "simpleIntegration": "Paprasta integracija", "getStartedCode": "Pradėkite su keliais kodo eilutėmis", "twentyPercentOff": "20% NUOLAIDA", "sslEncryption": "SSL šifravimas", "monitoring247": "24/7 s<PERSON><PERSON><PERSON><PERSON><PERSON>", "comprehensiveDocumentation": "Išsami dokumentacija", "postalApi": "PostalAPI", "invalidResetLink": "Netinkama nustatymo nuoroda", "invalidResetLinkMessage": "Ši slaptažodžio nustatymo nuoroda yra netinkama arba pasibaigė galiojimo laikas. Prašome užklausti naujos.", "requestNewResetLink": "Užklausti naujos nustatymo nuorodos", "backToLogin": "Grįžti prie prisijungimo", "rememberPassword": "Prisimenate slaptažodį?", "createAccount": "Sukurti paskyrą", "joinThousands": "Prisijunkite prie tūkstančių kūrėjų, naudojančių mūsų API", "alreadyHaveAccount": "Jau turite paskyrą?", "signInHere": "Prisijunkite čia", "dontHaveAccount": "Neturite paskyros?", "signUpHere": "Registruokit<PERSON><PERSON>", "clear": "Išvalyti", "terminal": "Terminalas"}, "footer": {"about": "Apie mus", "contact": "Kontaktai", "docs": "Dokumentacija", "privacyPolicy": "Privatumo politika", "termsOfService": "Paslaugų teikimo sąlygos", "checking": "Tikrinama...", "copyright": "<PERSON><PERSON><PERSON>", "copyrightLabel": "Autorių <PERSON>", "allSystemsOperational": "Visos sistemos ve<PERSON>a"}, "dashboard": {"welcomeBack": "S<PERSON><PERSON> sugrįžę, {name}!", "overviewText": "Čia yra jūsų API naudojimo ir paskyros būsen<PERSON>.", "totalRequests": "<PERSON><PERSON> viso <PERSON>", "thisMonth": "Šį mėnesį", "apiKeys": "API raktai", "plan": "Planas", "monthlyUsage": "M<PERSON><PERSON><PERSON>", "requestsUsed": "Panaud<PERSON><PERSON>", "requestsRemaining": "<PERSON><PERSON> {count} užklausų šį mėnesį", "manageApiKeys": "Tvarkykite savo API raktus ir prieigos <PERSON>us", "manageKeys": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "documentation": "Dokumentacija", "learnIntegrate": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip integruoti mūsų API", "viewDocs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>", "upgradePlan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getMoreRequests": "Gaukite daugiau užklausų ir funkcijų", "upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failedToLoad": "Nepavyko įkelti valdymo skydelio duomenų"}, "accessibility": {"findNearbyTerminals": "<PERSON><PERSON><PERSON> artimuosius terminalus", "selectProvider": "Pasirink<PERSON>", "searchTerminals": "Ieškoti terminalų", "closeAlert": "Uždaryti įspėjimą", "toggleTheme": "Perjungti temą", "englishFlag": "Anglijos vėliava", "lithuanianFlag": "Lietuvos vėliava"}, "contact": {"title": "Susisiekite su mumis", "subtitle": "Susisiekite su mūsų komanda dėl palaikymo ir užklausų", "getInTouch": "Susisiekite", "email": {"title": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>", "description": "Atsiųskite mums el. <PERSON> ir mes atsakysime kuo gre<PERSON>č<PERSON>u."}, "hours": {"title": "Darbo valandos", "description": "Mūsų palaikymo komanda dirba šiomis valandomis:", "weekdays": "Pirmadienis - Penktadienis", "weekends": "Šeštadienis - Sekmadienis", "closed": "Uždaryta"}, "response": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Stengiamės greitai atsakyti į visas užklausas.", "time": "Įprastas atsakymas: 24-48 valandos"}, "support": {"title": "Pa<PERSON>ldomi ištekliai", "documentation": "Dokumentacija", "docDescription": "Raskite atsakymus į dažnai užduodamus klausimus ir sužinokite, kaip integruoti mūsų API.", "viewDocs": "Žiūrėti dokumentaciją", "apiStatus": "API būsena", "statusDescription": "Patikrinkite dabartinę mūsų API paslaugų būseną.", "operational": "Visos sistemos ve<PERSON>a"}}}