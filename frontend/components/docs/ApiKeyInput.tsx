'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from '@/lib/translations';

interface ApiKeyInputProps {
  value: string;
  onChange: (value: string) => void;
}

export default function ApiKeyInput({ value, onChange }: ApiKeyInputProps) {
  const { t } = useTranslations();
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [showKey, setShowKey] = useState(false);

  // Validate API key format
  useEffect(() => {
    if (!value) {
      setIsValid(null);
      return;
    }

    // API key format: ptapi_ followed by 64-character hex string
    const apiKeyRegex = /^ptapi_[a-f0-9]{64}$/i;
    setIsValid(apiKeyRegex.test(value));
  }, [value]);

  // Load saved API key from localStorage
  useEffect(() => {
    const savedKey = localStorage.getItem('docs_api_key');
    if (savedKey && !value) {
      onChange(savedKey);
    }
  }, [onChange, value]);

  const handleChange = (newValue: string) => {
    onChange(newValue);
    // Save to localStorage for convenience
    if (newValue) {
      localStorage.setItem('docs_api_key', newValue);
    } else {
      localStorage.removeItem('docs_api_key');
    }
  };

  const clearKey = () => {
    handleChange('');
    localStorage.removeItem('docs_api_key');
  };

  return (
    <div className="form-control w-full">
      <label className="label">
        <span className="label-text font-semibold text-base-content">
          {t('docs.apiKey.title')}
        </span>
        {value && (
          <button
            onClick={clearKey}
            className="label-text-alt text-error hover:text-error/80 transition-colors"
          >
            {t('ui.clear')}
          </button>
        )}
      </label>
      
      <div className="relative">
        <input
          type={showKey ? 'text' : 'password'}
          placeholder={t('docs.apiKey.placeholder')}
          className={`input input-bordered w-full pr-20 ${
            isValid === false 
              ? 'input-error' 
              : isValid === true 
                ? 'input-success' 
                : ''
          }`}
          value={value}
          onChange={(e) => handleChange(e.target.value)}
        />
        
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
          {/* Toggle visibility button */}
          <button
            type="button"
            onClick={() => setShowKey(!showKey)}
            className="btn btn-ghost btn-sm btn-circle"
            title={showKey ? t('docs.apiKey.hideKey') : t('docs.apiKey.showKey')}
          >
            {showKey ? (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </button>
          
          {/* Status indicator */}
          {isValid !== null && (
            <div className="flex items-center">
              {isValid ? (
                <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Help text */}
      <label className="label">
        <span className={`label-text-alt text-xs break-all ${
          isValid === false ? 'text-error' : 'text-base-content/60'
        }`}>
          {isValid === false 
            ? t('docs.apiKey.invalid')
            : t('docs.apiKey.format')
          }
        </span>
      </label>
    </div>
  );
}
