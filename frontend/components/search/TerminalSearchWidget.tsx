'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from '@/lib/translations';
import { useToast } from '@/components/ui/Toast';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export interface TerminalSearchWidgetProps {
  onSearch?: (query: string, provider: string) => void;
  onNearbySearch?: (lat: number, lng: number) => void;
  className?: string;
  size?: 'default' | 'large';
  showProviderFilter?: boolean;
  placeholder?: string;
  initialQuery?: string;
  initialProvider?: string;
  disabled?: boolean;
}

export default function TerminalSearchWidget({
  onSearch,
  onNearbySearch,
  className = '',
  size = 'default',
  showProviderFilter = true,
  placeholder,
  initialQuery = '',
  initialProvider,
  disabled = false
}: TerminalSearchWidgetProps) {
  const { t } = useTranslations();
  const { showToast } = useToast();
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [selectedProvider, setSelectedProvider] = useState(initialProvider || t('ui.allProviders'));
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearch = () => {
    if (disabled) return;
    onSearch?.(searchQuery, selectedProvider);
  };

  const handleNearbySearch = () => {
    if (disabled || !navigator.geolocation) {
      showToast(t('ui.geolocationNotSupported'), 'error');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        onNearbySearch?.(latitude, longitude);
        showToast(t('ui.locationFound'), 'success');
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Error getting location:', error);
        showToast(t('ui.locationError'), 'error');
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !disabled) {
      handleSearch();
    }
  };

  // Size-based styling
  const sizeClasses = {
    default: {
      container: 'max-w-2xl',
      input: 'h-12 pl-4 pr-12 text-base',
      button: 'h-12 px-6 min-w-[120px] text-base',
      dropdown: 'h-12 px-4 min-w-[140px] text-base',
      icon: 'w-4 h-4',
      spacing: 'p-2 gap-2'
    },
    large: {
      container: 'max-w-4xl',
      input: 'h-12 sm:h-16 pl-4 sm:pl-6 pr-12 sm:pr-14 text-base sm:text-lg',
      button: 'h-12 sm:h-16 px-5 sm:px-8 min-w-[100px] sm:min-w-[140px] text-sm sm:text-lg',
      dropdown: 'h-12 sm:h-16 px-3 sm:px-6 lg:min-w-[160px] text-sm sm:text-base',
      icon: 'w-4 h-4 sm:w-5 sm:h-5',
      spacing: 'p-2 gap-2'
    }
  };

  const styles = sizeClasses[size];

  return (
    <div className={`${styles.container} mx-auto ${className}`}>
      <div className="bg-base-100/95 backdrop-blur-md rounded-2xl sm:rounded-3xl shadow-2xl border border-base-300 hover:shadow-3xl transition-all duration-300">
        <div className={`${styles.spacing}`}>
          <div className="flex flex-col lg:flex-row gap-2">
            {/* Search Input */}
            <div className="flex-1 relative">
              <input 
                type="text" 
                placeholder={placeholder || t('landing.hero.searchPlaceholder')}
                className={`input w-full ${styles.input} border-0 bg-base-200/30 focus:bg-base-200/50 focus:outline-none rounded-xl sm:rounded-2xl placeholder:text-base-content/60 transition-colors duration-200`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={disabled}
              />
              <button
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm btn-circle hover:bg-primary/10 transition-all duration-200 min-h-[44px] min-w-[44px] ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleNearbySearch}
                disabled={disabled || isGettingLocation}
                title={t('accessibility.findNearbyTerminals')}
                aria-label={t('accessibility.findNearbyTerminals')}
              >
                {isGettingLocation ? (
                  <LoadingSpinner size="sm" color="primary" />
                ) : (
                  <svg className={`${styles.icon} text-primary`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                )}
              </button>
            </div>
            
            {/* Provider Dropdown and Search Button */}
            <div className="flex gap-2">
              {/* Provider Dropdown */}
              {showProviderFilter && (
                <div className="relative flex-1 lg:flex-none" ref={dropdownRef}>
                  <button
                    className={`btn btn-outline ${styles.dropdown} w-full rounded-xl sm:rounded-2xl border-base-300 bg-base-200/20 hover:border-primary hover:bg-primary/10 transition-all duration-200 flex items-center justify-between font-medium min-h-[44px] ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
                    disabled={disabled}
                    aria-label={t('accessibility.selectProvider')}
                    aria-expanded={isDropdownOpen}
                    aria-haspopup="listbox"
                  >
                    <span className="font-medium truncate">{selectedProvider}</span>
                    <svg className={`w-4 h-4 ml-1 sm:ml-2 transition-transform duration-200 flex-shrink-0 ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {isDropdownOpen && !disabled && (
                    <div className="absolute top-full left-0 right-0 mt-2 bg-base-100 rounded-2xl shadow-xl border border-base-300 z-50 overflow-hidden">
                      <div className="py-2">
                        {[t('ui.allProviders'), t('ui.lpExpress'), t('ui.omniva'), t('ui.dpd'), t('ui.venipak')].map((provider) => (
                          <button
                            key={provider}
                            className="w-full px-4 py-3 text-left hover:bg-primary/5 transition-colors duration-150 text-sm sm:text-base min-h-[44px] flex items-center"
                            onClick={() => {
                              setSelectedProvider(provider);
                              setIsDropdownOpen(false);
                            }}
                            role="option"
                            aria-selected={selectedProvider === provider}
                          >
                            {provider}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Search Button */}
              <button
                className={`btn btn-primary ${styles.button} rounded-xl font-semibold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 min-h-[44px] flex-shrink-0 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleSearch}
                disabled={disabled}
                aria-label={t('accessibility.searchTerminals')}
              >
                <svg className={`${styles.icon} mr-1 sm:mr-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span className="hidden sm:inline">{t('ui.search')}</span>
                <span className="sm:hidden">{t('ui.go')}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
