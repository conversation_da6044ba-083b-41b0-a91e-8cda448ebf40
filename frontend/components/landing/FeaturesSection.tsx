import { useTranslations } from '@/lib/translations';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export default function FeaturesSection() {
  const { t } = useTranslations();

  const features = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: t('ui.realTimeData'),
      description: t('ui.accessUpToDate'),
      colorClass: 'text-info',
      bgClass: 'bg-info/10'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      title: t('ui.packageTracking'),
      description: t('ui.trackPackages'),
      colorClass: 'text-success',
      bgClass: 'bg-success/10'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: t('ui.lightningFast'),
      description: t('ui.optimizedApi'),
      colorClass: 'text-warning',
      bgClass: 'bg-warning/10'
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      ),
      title: t('ui.easyTerminalSearch'),
      description: t('ui.easilySearch'),
      colorClass: 'text-secondary',
      bgClass: 'bg-secondary/10'
    }
  ];

  return (
    <section id="features" className="py-20 bg-base-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            {t('landing.features.title')}
          </h2>
          <p className="text-xl text-base-content/70 max-w-2xl mx-auto">
            {t('landing.features.subtitle')}
          </p>
        </div>
        
        <div className="max-w-5xl mx-auto bg-base-100 rounded-2xl shadow-xl border border-base-300 p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`flex items-start gap-4 p-6 rounded-xl hover:scale-105 transition-transform duration-200 ${feature.bgClass}`}
              >
                <div className={`${feature.colorClass} p-3 rounded-full bg-base-100 shadow-sm flex-shrink-0`}>
                  {feature.icon}
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-base-content mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-base-content/70 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-8">
            <div className="badge badge-soft badge-outline badge-primary badge-lg px-6 py-3 text-sm sm:text-base font-medium shadow-sm cursor-pointer">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              <span className="text-center">{t('ui.startBuildingToday')}</span>
            </div>
          </div>
        </div>
        
        {/* Code Example */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4 text-base-content">{t('ui.simpleIntegration')}</h3>
            <p className="text-xl text-base-content/70">{t('ui.getStartedCode')}</p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-base-200 dark:bg-base-300 rounded-xl shadow-2xl overflow-hidden border border-base-300 dark:border-base-content/20">
              <div className="bg-base-300 dark:bg-base-200 px-6 py-3 flex items-center gap-2 border-b border-base-300 dark:border-base-content/20">
                <div className="w-3 h-3 bg-error rounded-full"></div>
                <div className="w-3 h-3 bg-warning rounded-full"></div>
                <div className="w-3 h-3 bg-success rounded-full"></div>
                <span className="ml-4 text-base-content/80 text-sm font-mono font-medium">{t('ui.terminal')}</span>
              </div>
              <div className="p-6 font-mono text-sm leading-relaxed">
                <div className="text-success font-medium mb-1">{`$ curl -X GET "${API_BASE_URL}/api/v1/terminals?limit=1" \\`}</div>
                <div className="text-base-content/70 mb-1 ml-6">-H "X-API-Key: YOUR_API_KEY" \</div>
                <div className="text-base-content/70 mb-4 ml-6">-H "Content-Type: application/json"</div>
                <div className="leading-relaxed">
                  <div className="text-base-content/70">{`{`}</div>
                  <div className="ml-2">
                    <span className="text-primary font-medium">"data"</span><span className="text-base-content/70">: [</span>
                  </div>
                  <div className="ml-4">
                    <span className="text-base-content/70">{`{`}</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"id"</span><span className="text-base-content/70">: </span><span className="text-success">"lp_4205"</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"name"</span><span className="text-base-content/70">: </span><span className="text-success">"Aibė"</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"city"</span><span className="text-base-content/70">: </span><span className="text-success">"Kaišiadorys"</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"address"</span><span className="text-base-content/70">: </span><span className="text-success">"Vytauto Didžiojo g.35"</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"coordinates"</span><span className="text-base-content/70">: {`{`}</span>
                  </div>
                  <div className="ml-8">
                    <span className="text-primary font-medium">"lat"</span><span className="text-base-content/70">: </span><span className="text-info">54.8653</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-8">
                    <span className="text-primary font-medium">"lng"</span><span className="text-base-content/70">: </span><span className="text-info">24.4607</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-base-content/70">{`},`}</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"provider"</span><span className="text-base-content/70">: </span><span className="text-success">"LP_EXPRESS"</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-6">
                    <span className="text-primary font-medium">"terminalType"</span><span className="text-base-content/70">: </span><span className="text-success">"PARCEL_LOCKER"</span>
                  </div>
                  <div className="ml-4">
                    <span className="text-base-content/70">{`}`}</span>
                  </div>
                  <div className="ml-2">
                    <span className="text-base-content/70">],</span>
                  </div>
                  <div className="ml-2">
                    <span className="text-primary font-medium">"pagination"</span><span className="text-base-content/70">: {`{`}</span>
                  </div>
                  <div className="ml-4">
                    <span className="text-primary font-medium">"page"</span><span className="text-base-content/70">: </span><span className="text-info">1</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-4">
                    <span className="text-primary font-medium">"limit"</span><span className="text-base-content/70">: </span><span className="text-info">1</span><span className="text-base-content/70">,</span>
                  </div>
                  <div className="ml-4">
                    <span className="text-primary font-medium">"total"</span><span className="text-base-content/70">: </span><span className="text-info">1923</span>
                  </div>
                  <div className="ml-2">
                    <span className="text-base-content/70">{`}`}</span>
                  </div>
                  <div className="text-base-content/70">{`}`}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}