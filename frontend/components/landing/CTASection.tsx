'use client';

import Link from 'next/link';
import { useTranslations } from '@/lib/translations';

export default function CTASection() {
  const { t } = useTranslations();

  return (
    <section className="py-20 bg-gradient-to-br from-primary via-secondary to-accent relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10 dark:opacity-5">
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-base-content/20 rounded-full animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-base-content/15 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-base-content/10 rounded-full animate-pulse delay-500"></div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-primary-content mb-6 drop-shadow-lg">
            {t('ui.readyToStart')}
          </h2>
          <p className="text-xl text-primary-content/90 max-w-2xl mx-auto drop-shadow-md">
            {t('ui.choosePlan')}
          </p>
        </div>
          
        <div className="flex flex-col sm:flex-row gap-6 justify-center mb-20">
          <Link
            href="/auth/register"
            className="btn btn-primary btn-lg px-4 sm:px-8 py-4 font-semibold min-h-[56px] rounded-xl shadow-xl transition-all duration-200 hover:scale-105 border-0 text-sm sm:text-base"
          >
            <span className="hidden sm:inline">{t('ui.startBuilding')}</span>
            <span className="sm:hidden">{t('ui.getStarted')}</span>
            <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>

          <a
            href="/docs"
            className="btn btn-secondary btn-lg px-4 sm:px-8 py-4 font-semibold min-h-[56px] rounded-xl transition-all duration-200 hover:scale-105 text-sm sm:text-base"
          >
            <span className="hidden sm:inline">{t('ui.viewDocumentation')}</span>
            <span className="sm:hidden">{t('ui.docs')}</span>
          </a>
        </div>
          
        <div className="flex flex-col sm:flex-row gap-8 sm:gap-12 justify-center items-center text-center">
          <div className="flex items-center gap-3">
            <svg className="w-5 h-5 text-success drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-lg font-medium text-primary-content/95 drop-shadow-sm">{t('ui.fastApiResponse')}</span>
          </div>
          <div className="flex items-center gap-3">
            <svg className="w-5 h-5 text-info drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
            <span className="text-lg font-medium text-primary-content/95 drop-shadow-sm">{t('ui.developerSupport')}</span>
          </div>
          <div className="flex items-center gap-3">
            <svg className="w-5 h-5 text-warning drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <span className="text-lg font-medium text-primary-content/95 drop-shadow-sm">{t('ui.secureReliable')}</span>
          </div>
        </div>
      </div>
    </section>
  );
}