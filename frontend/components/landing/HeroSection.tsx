'use client';

import Link from 'next/link';
import { useTranslations } from '@/lib/translations';
import TerminalSearchWidget from '@/components/search/TerminalSearchWidget';

export default function HeroSection() {
  const { t } = useTranslations();

  const handleSearch = (query: string, provider: string) => {
    // Redirect to find-terminals page with query parameters
    const searchParams = new URLSearchParams();
    if (query) searchParams.append('q', query);
    if (provider && provider !== t('ui.allProviders')) searchParams.append('provider', provider);

    window.location.href = `/find-terminals?${searchParams.toString()}`;
  };

  const handleNearbySearch = (lat: number, lng: number) => {
    // Redirect to find-terminals page with location parameters
    const searchParams = new URLSearchParams();
    searchParams.append('lat', lat.toString());
    searchParams.append('lng', lng.toString());
    searchParams.append('radius', '10'); // Default 10km radius

    window.location.href = `/find-terminals?${searchParams.toString()}`;
  };

  return (
    <section className="hero min-h-[80vh] sm:min-h-[85vh] bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-10 sm:top-20 left-5 sm:left-10 w-48 h-48 sm:w-72 sm:h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 sm:bottom-20 right-5 sm:right-10 w-64 h-64 sm:w-96 sm:h-96 bg-secondary/10 rounded-full blur-3xl"></div>
      
      <div className="hero-content text-center relative z-10 px-4">
        <div className="max-w-5xl w-full">
          <div className="mb-6 flex justify-center">
            <div className="badge  badge-soft badge-outline badge-primary badge-lg px-6 py-3 text-sm sm:text-base font-medium shadow-sm cursor-pointer">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <span>{t('landing.hero.badge')}</span>
            </div>
          </div>
          
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent leading-tight">
            {t('landing.hero.title')}
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl mb-8 sm:mb-10 text-base-content/70 max-w-3xl mx-auto leading-relaxed font-light px-4">
            {t('landing.hero.subtitle')}
          </p>
          
          {/* Enhanced Search Interface */}
          <TerminalSearchWidget
            onSearch={handleSearch}
            onNearbySearch={handleNearbySearch}
            size="large"
            className="mb-8 sm:mb-12 px-2 sm:px-4"
          />
          
          {/* Feature Badges */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 sm:mb-12 px-2">
            <div className="badge badge-soft badge-primary px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {t('landing.hero.realTimeData') || 'Real-time Data'}
            </div>
            <div className="badge badge-soft badge-accent px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              {t('landing.hero.packageTracking') || 'Package Tracking'}
            </div>
            <div className="badge badge-soft badge-success px-4 py-3 text-sm font-medium flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              {t('landing.hero.fastApi') || 'Fast API'}
            </div>
          </div>
          
          {/* Enhanced CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 justify-center items-center px-2 sm:px-4">
            <Link
              href="/auth/register"
              className="btn btn-primary btn-md sm:btn-lg px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 group w-full sm:w-auto min-h-[44px]"
            >
              {t('landing.hero.cta')}
              <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
            <Link
              href="#docs"
              className="btn btn-outline btn-md sm:btn-lg px-6 sm:px-10 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-xl hover:scale-105 transition-all duration-300 group w-full sm:w-auto min-h-[44px]"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {t('landing.hero.viewDocs')}
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}