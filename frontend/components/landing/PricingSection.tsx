'use client';

import Link from 'next/link';
import { useState } from 'react';
import { useTranslations } from '@/lib/translations';

export default function PricingSection() {
  const { t } = useTranslations();
  const [isYearly, setIsYearly] = useState(false);

  const plans = [
    {
      name: t('landing.pricing.starter.name'),
      price: '$19',
      period: t('landing.pricing.starter.period'),
      yearlyPrice: '$15',
      description: t('landing.pricing.starter.description'),
      features: [
        t('landing.pricing.starter.features.0'),
        t('landing.pricing.starter.features.1'),
        t('landing.pricing.starter.features.2'),
        t('landing.pricing.starter.features.3')
      ],
      cta: t('landing.pricing.starter.cta'),
      href: '/auth/register?plan=starter',
      popular: false,
      buttonClass: 'btn-outline'
    },
    {
      name: t('landing.pricing.pro.name'),
      price: '$49',
      period: t('landing.pricing.pro.period'),
      yearlyPrice: '$39',
      description: t('landing.pricing.pro.description'),
      features: [
        t('landing.pricing.pro.features.0'),
        t('landing.pricing.pro.features.1'),
        t('landing.pricing.pro.features.2'),
        t('landing.pricing.pro.features.3'),
        t('landing.pricing.pro.features.4'),
        t('landing.pricing.pro.features.5')
      ],
      cta: t('landing.pricing.pro.cta'),
      href: '/auth/register?plan=pro',
      popular: true,
      buttonClass: 'btn-primary'
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-base-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-base-content">
            {t('landing.pricing.title')}
          </h2>
          <p className="text-xl text-base-content/70 max-w-2xl mx-auto mb-8">
            {t('landing.pricing.subtitle')}
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 flex-wrap">
            <span className={`text-lg ${!isYearly ? 'font-semibold' : 'text-base-content/70'}`}>
              {t('landing.pricing.monthly')}
            </span>
            <input
              type="checkbox"
              className="toggle toggle-primary"
              checked={isYearly}
              onChange={(e) => setIsYearly(e.target.checked)}
            />
            <span className={`text-lg ${isYearly ? 'font-semibold' : 'text-base-content/70'}`}>
              {t('landing.pricing.yearly')}
            </span>
            <div className={`badge ${isYearly ? 'badge-accent' : 'badge-ghost'} transition-all duration-300`}>
              <span className={isYearly ? 'text-success-content' : 'text-base-content/60'}>
                {t('ui.twentyPercentOff')}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl w-full justify-items-center">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 w-full max-w-sm ${
                plan.popular ? 'border-2 border-primary scale-105' : 'border border-base-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="badge badge-primary badge-lg px-4 py-2">
                    {t('landing.pricing.pro.popular')}
                  </div>
                </div>
              )}
              
              <div className="card-body">
                <h3 className="card-title text-2xl mb-2">{plan.name}</h3>
                <p className="text-base-content/70 mb-6">{plan.description}</p>
                
                <div className="mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold">
                      {isYearly && plan.yearlyPrice !== plan.price ? plan.yearlyPrice : plan.price}
                    </span>
                    {plan.period && (
                      <span className="text-base-content/70 ml-1">
                        {plan.period}
                      </span>
                    )}
                  </div>
                  {isYearly && plan.yearlyPrice !== plan.price && (
                    <div className="text-sm text-base-content/50 line-through">
                      {plan.price}{plan.period}
                    </div>
                  )}
                </div>
                
                <ul className="space-y-3 mb-8 flex-grow">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <div className="card-actions">
                  <Link
                    href={plan.href}
                    className={`btn ${plan.buttonClass} btn-block btn-lg min-h-[44px] rounded-xl`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              </div>
            </div>
          ))}
          </div>
        </div>
        
        {/* FAQ or Additional Info */}
        <div className="text-center mt-16">
          <p className="text-base-content/70 mb-4">{t('landing.pricing.allPlansInclude')}</p>
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-base-content">{t('ui.sslEncryption')}</span>
            </div>
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-base-content">{t('ui.monitoring247')}</span>
            </div>
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-base-content">{t('ui.comprehensiveDocumentation')}</span>
            </div>
          </div>

        </div>
      </div>
    </section>
  );
}