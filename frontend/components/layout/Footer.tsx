'use client';

import { useTranslations } from '@/lib/translations';
import { useSystemStatus } from '@/lib/hooks/useSystemStatus';

export default function Footer() {
  const { t } = useTranslations();
  const currentYear = new Date().getFullYear();
  const { status, message, isLoading } = useSystemStatus();

  return (
    <footer className="bg-base-200 text-base-content mt-auto border-t border-base-300">
      <div className="container mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-6 sm:py-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">

            {/* Navigation Links */}
            <div className="flex flex-wrap justify-center sm:justify-start gap-x-6 gap-y-2">
              <a href="/about" className="link link-hover text-sm py-1 min-h-[44px] flex items-center hover:text-primary transition-colors">
                {t('footer.about')}
              </a>
              <a href="/contact" className="link link-hover text-sm py-1 min-h-[44px] flex items-center hover:text-primary transition-colors">
                {t('navigation.contact')}
              </a>
              <a href="/docs" className="link link-hover text-sm py-1 min-h-[44px] flex items-center hover:text-primary transition-colors">
                {t('navigation.docs')}
              </a>
              <a href="/privacy" className="link link-hover text-sm py-1 min-h-[44px] flex items-center hover:text-primary transition-colors">
                {t('footer.privacyPolicy')}
              </a>
              <a href="/terms" className="link link-hover text-sm py-1 min-h-[44px] flex items-center hover:text-primary transition-colors">
                {t('footer.termsOfService')}
              </a>
            </div>

            {/* System Status */}
            <div className="flex items-center gap-2">
              {isLoading ? (
                <div className="w-3 h-3 bg-gray-400 rounded-full animate-pulse"></div>
              ) : (
                <div className={`w-3 h-3 rounded-full ${
                  status === 'healthy' ? 'bg-green-400' :
                  status === 'degraded' ? 'bg-warning' :
                  status === 'unhealthy' ? 'bg-error' :
                  'bg-gray-400'
                }`}></div>
              )}
              <span className="text-sm text-base-content/70">
                {isLoading ? t('footer.checking') : message}
              </span>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-base-300 py-4">
          <div className="text-center">
            <p className="text-xs sm:text-sm text-base-content/60">
              Copyright © {currentYear} - {process.env.NEXT_PUBLIC_APP_NAME || 'Postal Terminal API'}. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}