'use client';

import { useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

interface DeleteAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DeleteAccountModal({ isOpen, onClose }: DeleteAccountModalProps) {
  const t = useTranslations('profile');
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const { deleteAccount, user } = useAuthStore();

  const handleDeleteAccount = async () => {
    if (confirmText !== 'DELETE' || !user) return;

    setIsDeleting(true);
    try {
      await deleteAccount();
      // User will be logged out automatically by the store
      router.push('/');
    } catch (error) {
      console.error('Account deletion failed:', error);
      setIsDeleting(false);
      // You might want to show an error toast here
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setConfirmText('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal modal-open">
      <div className="modal-box">
        <h3 className="font-bold text-lg text-error">
          {t('deleteAccount')}
        </h3>
        
        <div className="py-4">
          <div className="alert alert-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h4 className="font-bold">{t('deleteAccountWarning')}</h4>
              <p className="text-sm">{t('deleteAccountDescription')}</p>
            </div>
          </div>

          <p className="mb-4">
            {t('deleteAccountConfirmation')}
          </p>

          <div className="form-control">
            <label className="label">
              <span className="label-text">
                {t('typeDeleteToConfirm')}
              </span>
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="DELETE"
              className="input input-bordered"
              disabled={isDeleting}
            />
          </div>
        </div>

        <div className="modal-action">
          <button
            type="button"
            onClick={handleClose}
            className="btn btn-ghost"
            disabled={isDeleting}
          >
            {t('cancel')}
          </button>
          <button
            type="button"
            onClick={handleDeleteAccount}
            disabled={confirmText !== 'DELETE' || isDeleting}
            className={`btn btn-error ${isDeleting ? 'loading' : ''}`}
          >
            {isDeleting ? t('deleting') : t('deleteAccount')}
          </button>
        </div>
      </div>
    </div>
  );
}
