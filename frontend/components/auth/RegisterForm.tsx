'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from '@/lib/translations';
import { useRouter } from 'next/navigation';
import GoogleOAuthButton from './GoogleOAuthButton';

export default function RegisterForm() {
  const { t } = useTranslations();
  
  const registerSchema = z.object({
    firstName: z.string().min(2, t('auth.validation.firstNameMinLength')),
    lastName: z.string().min(2, t('auth.validation.lastNameMinLength')),
    email: z.string().email(t('auth.validation.invalidEmail')),
    password: z.string().min(8, t('auth.validation.passwordMinLengthRegister'))
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, t('auth.validation.passwordComplexity')),
    confirmPassword: z.string(),
    acceptTerms: z.boolean().refine(val => val === true, t('auth.validation.acceptTerms'))
  }).refine((data) => data.password === data.confirmPassword, {
    message: t('auth.validation.passwordsNoMatch'),
    path: ["confirmPassword"]
  });

  type RegisterFormData = z.infer<typeof registerSchema>;
  const router = useRouter();
  const { register: registerUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema)
  });

  const password = watch('password');

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    setError('');

    try {
      await registerUser({
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        password: data.password,
        confirmPassword: data.confirmPassword
      });
      router.push('/auth/verify-email?email=' + encodeURIComponent(data.email));
    } catch (err: any) {
      setError(err.message || t('auth.errors.registrationFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: '', color: '' };
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^\w\s]/.test(password)) strength++;
    
    const labels = [t('auth.passwordStrength.veryWeak'), t('auth.passwordStrength.weak'), t('auth.passwordStrength.fair'), t('auth.passwordStrength.good'), t('auth.passwordStrength.strong')];
    const colors = ['error', 'warning', 'warning', 'success', 'success'];
    
    return {
      strength: (strength / 5) * 100,
      label: labels[strength - 1] || '',
      color: colors[strength - 1] || 'error'
    };
  };

  const passwordStrength = getPasswordStrength(password);

  return (
    <div className="card w-full max-w-md bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">
            {t('auth.register.title')}
          </h2>
          <p className="text-base-content/70 text-sm">
            {t('auth.register.subtitle')}
          </p>
        </div>
        
        {error && (
          <div className="alert alert-error mb-4">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error}</span>
          </div>
        )}
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">{t('auth.register.firstName')}</span>
              </label>
              <input
                type="text"
                placeholder={t('auth.register.firstNamePlaceholder')}
                className={`input input-bordered input-lg w-full ${
                  errors.firstName ? 'input-error' : ''
                }`}
                {...register('firstName')}
              />
              {errors.firstName && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.firstName.message}
                  </span>
                </label>
              )}
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text">{t('auth.register.lastName')}</span>
              </label>
              <input
                type="text"
                placeholder={t('auth.register.lastNamePlaceholder')}
                className={`input input-bordered input-lg w-full ${
                  errors.lastName ? 'input-error' : ''
                }`}
                {...register('lastName')}
              />
              {errors.lastName && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {errors.lastName.message}
                  </span>
                </label>
              )}
            </div>
          </div>

          {/* Email Field */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">{t('auth.register.email')}</span>
            </label>
            <input
              type="email"
              placeholder={t('auth.register.emailPlaceholder')}
              className={`input input-bordered input-lg w-full ${
                errors.email ? 'input-error' : ''
              }`}
              {...register('email')}
            />
            {errors.email && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.email.message}
                </span>
              </label>
            )}
          </div>

          {/* Password Field */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">{t('auth.register.password')}</span>
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder={t('auth.register.passwordPlaceholder')}
                className={`input input-bordered input-lg w-full pr-12 ${
                  errors.password ? 'input-error' : ''
                }`}
                {...register('password')}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            
            {/* Password Strength Indicator */}
            {password && (
              <div className="mt-2">
                <div className="flex justify-between text-xs mb-1">
                  <span>{t('auth.passwordStrength.label')}</span>
                  <span className={`text-${passwordStrength.color}`}>
                    {passwordStrength.label}
                  </span>
                </div>
                <progress 
                  className={`progress progress-${passwordStrength.color} w-full`} 
                  value={passwordStrength.strength} 
                  max="100"
                ></progress>
              </div>
            )}
            
            {errors.password && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.password.message}
                </span>
              </label>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">{t('auth.register.confirmPassword')}</span>
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder={t('auth.register.confirmPasswordPlaceholder')}
                className={`input input-bordered input-lg w-full pr-12 ${
                  errors.confirmPassword ? 'input-error' : ''
                }`}
                {...register('confirmPassword')}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.confirmPassword.message}
                </span>
              </label>
            )}
          </div>

          {/* Terms and Conditions */}
          <div className="form-control">
            <label className="label cursor-pointer justify-start">
              <input
                type="checkbox"
                className={`checkbox checkbox-primary mr-3 ${
                  errors.acceptTerms ? 'checkbox-error' : ''
                }`}
                {...register('acceptTerms')}
              />
              <span className="label-text text-sm">
                {t('auth.register.acceptTerms')}{' '}
                <Link href="/terms" className="link link-primary">
                  {t('auth.register.termsLink')}
                </Link>
                {' '}{t('auth.register.and')}{' '}
                <Link href="/privacy" className="link link-primary">
                  {t('auth.register.privacyLink')}
                </Link>
              </span>
            </label>
            {errors.acceptTerms && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.acceptTerms.message}
                </span>
              </label>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            className={`btn btn-primary btn-lg w-full ${
              isLoading ? 'loading' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? '' : t('auth.register.submit')}
          </button>
        </form>

        {/* Divider */}
        <div className="divider">{t('auth.register.or')}</div>

        {/* Social Registration */}
        <GoogleOAuthButton
          className="btn-lg"
          disabled={isLoading}
        />

        {/* Sign In Link */}
        <div className="text-center mt-6">
          <span className="text-sm text-base-content/70">
            {t('auth.register.hasAccount')}{' '}
          </span>
          <Link href="/auth/login" className="link link-primary text-sm">
            {t('auth.register.signIn')}
          </Link>
        </div>
      </div>
    </div>
  );
}