'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { FileText, Filter, Download, RefreshCw, AlertTriangle, Info, AlertCircle, Bug } from 'lucide-react';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  source: string;
  metadata?: any;
  user_id?: string;
  request_id?: string;
}

export default function AdminLogsPage() {
  const { user: currentUser } = useAuthStore();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    level: '' as '' | 'error' | 'warn' | 'info' | 'debug',
    search: '',
    since: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0
  });

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      fetchLogs();
    }
  }, [currentUser, pagination.page, pagination.limit, filters]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // For now, we'll use mock data since the backend endpoint is not fully implemented
      const mockLogs: LogEntry[] = [
        {
          id: '1',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          level: 'error',
          message: 'Database connection timeout',
          source: 'database',
          metadata: { connection_pool: 'primary', timeout_ms: 5000 },
          request_id: 'req-abc123'
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          level: 'warn',
          message: 'API rate limit exceeded for user',
          source: 'api',
          metadata: { user_id: 'user123', endpoint: '/api/v1/terminals', limit: 100 },
          user_id: 'user123',
          request_id: 'req-def456'
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          level: 'info',
          message: 'User successfully authenticated',
          source: 'auth',
          metadata: { login_method: 'email', ip_address: '*************' },
          user_id: 'user456',
          request_id: 'req-ghi789'
        },
        {
          id: '4',
          timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
          level: 'debug',
          message: 'Cache miss for terminal search query',
          source: 'cache',
          metadata: { query: 'postal_code:12345', cache_key: 'terminals:12345' },
          request_id: 'req-jkl012'
        },
        {
          id: '5',
          timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
          level: 'error',
          message: 'Failed to send email notification',
          source: 'email',
          metadata: { recipient: '<EMAIL>', template: 'welcome', smtp_error: 'Connection refused' },
          request_id: 'req-mno345'
        }
      ];

      // Apply filters
      let filteredLogs = mockLogs;
      
      if (filters.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filters.level);
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredLogs = filteredLogs.filter(log =>
          log.message.toLowerCase().includes(searchLower) ||
          log.source.toLowerCase().includes(searchLower) ||
          log.request_id?.toLowerCase().includes(searchLower)
        );
      }

      if (filters.since) {
        const sinceDate = new Date(filters.since);
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= sinceDate);
      }

      setLogs(filteredLogs);
      setPagination(prev => ({
        ...prev,
        total: filteredLogs.length
      }));
    } catch (err: any) {
      console.error('Failed to fetch logs:', err);
      setError(err.message || 'Failed to load system logs');
    } finally {
      setLoading(false);
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return <AlertTriangle className="w-4 h-4 text-error" />;
      case 'warn': return <AlertCircle className="w-4 h-4 text-warning" />;
      case 'info': return <Info className="w-4 h-4 text-info" />;
      case 'debug': return <Bug className="w-4 h-4 text-base-content opacity-60" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getLevelBadge = (level: string) => {
    switch (level) {
      case 'error': return 'badge-error';
      case 'warn': return 'badge-warning';
      case 'info': return 'badge-info';
      case 'debug': return 'badge-ghost';
      default: return 'badge-ghost';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const handleExportLogs = async () => {
    try {
      // In a real implementation, this would call the backend API
      const dataStr = JSON.stringify(logs, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Failed to export logs:', err);
      setError(err.message || 'Failed to export logs');
    }
  };

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access system logs.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">System Logs</h1>
            <p className="opacity-70">Monitor system activity and troubleshoot issues</p>
          </div>
          <div className="flex gap-2">
            <button
              className="btn btn-outline"
              onClick={handleExportLogs}
              disabled={loading || logs.length === 0}
            >
              <Download className="w-5 h-5" />
              Export
            </button>
            <button
              className="btn btn-primary"
              onClick={fetchLogs}
              disabled={loading}
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                <span className="font-medium">Filters:</span>
              </div>
              
              <div className="form-control">
                <input
                  type="text"
                  placeholder="Search logs..."
                  className="input input-bordered w-full max-w-xs"
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>

              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={filters.level}
                  onChange={(e) => setFilters({ ...filters, level: e.target.value as any })}
                >
                  <option value="">All Levels</option>
                  <option value="error">Error</option>
                  <option value="warn">Warning</option>
                  <option value="info">Info</option>
                  <option value="debug">Debug</option>
                </select>
              </div>

              <div className="form-control">
                <input
                  type="datetime-local"
                  className="input input-bordered"
                  value={filters.since}
                  onChange={(e) => setFilters({ ...filters, since: e.target.value })}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Logs List */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
              </div>
            ) : logs.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-16 h-16 mx-auto opacity-50 mb-4" />
                <h3 className="text-lg font-medium">No Logs Found</h3>
                <p className="opacity-70">No logs match your current filters.</p>
              </div>
            ) : (
              <div className="space-y-2">
                {logs.map((log) => (
                  <div
                    key={log.id}
                    className="border rounded-lg p-4 hover:bg-base-50 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      {getLevelIcon(log.level)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <div className={`badge ${getLevelBadge(log.level)}`}>
                            {log.level.toUpperCase()}
                          </div>
                          <span className="text-sm font-medium">{log.source}</span>
                          <span className="text-sm opacity-60">{formatTimestamp(log.timestamp)}</span>
                          {log.request_id && (
                            <span className="text-xs font-mono bg-base-200 px-2 py-1 rounded">
                              {log.request_id}
                            </span>
                          )}
                        </div>
                        
                        <p className="text-sm mb-2">{log.message}</p>
                        
                        {log.metadata && (
                          <details className="text-xs">
                            <summary className="cursor-pointer opacity-70 hover:opacity-100">
                              Show metadata
                            </summary>
                            <pre className="mt-2 p-2 bg-base-200 rounded overflow-x-auto">
                              {JSON.stringify(log.metadata, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination.total > pagination.limit && (
              <div className="flex justify-center mt-6">
                <div className="join">
                  <button
                    className="join-item btn"
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1 || loading}
                  >
                    Previous
                  </button>
                  <button className="join-item btn btn-active">
                    Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
                  </button>
                  <button
                    className="join-item btn"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit) || loading}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
