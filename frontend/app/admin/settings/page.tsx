'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Settings, Shield, AlertTriangle, CheckCircle, Activity, Database } from 'lucide-react';

interface SystemSettings {
  maintenance_mode: boolean;
  api_rate_limits: {
    default_per_minute: number;
    default_per_day: number;
    burst_limit: number;
  };
  email_settings: {
    smtp_enabled: boolean;
    from_email: string;
    from_name: string;
  };
  security_settings: {
    password_min_length: number;
    require_email_verification: boolean;
    session_timeout_minutes: number;
  };
  system_info: {
    version: string;
    uptime: number;
    database_status: 'healthy' | 'warning' | 'error';
    redis_status: 'healthy' | 'warning' | 'error';
  };
}

export default function AdminSettingsPage() {
  const { user: currentUser } = useAuthStore();
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    fetchSettings();
  }, [currentUser]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch real settings from API
      const settingsData = await apiClient.getSystemSettings();

      // Transform the settings array into the expected structure
      const mockSettings: SystemSettings = {
        maintenance_mode: false,
        api_rate_limits: {
          default_per_minute: 60,
          default_per_day: 10000,
          burst_limit: 100
        },
        email_settings: {
          smtp_enabled: true,
          from_email: '<EMAIL>',
          from_name: 'Postal Terminal API'
        },
        security_settings: {
          password_min_length: 8,
          require_email_verification: true,
          session_timeout_minutes: 1440
        },
        system_info: {
          version: '1.0.0',
          uptime: 99.9,
          database_status: 'healthy',
          redis_status: 'healthy'
        }
      };

      // Apply actual settings from API if available
      if (settingsData && settingsData.data && Array.isArray(settingsData.data)) {
        settingsData.data.forEach((setting: any) => {
          try {
            const value = JSON.parse(setting.value);
            switch (setting.key) {
              case 'maintenance_mode':
                mockSettings.maintenance_mode = value;
                break;
              case 'api_rate_limits':
                mockSettings.api_rate_limits = { ...mockSettings.api_rate_limits, ...value };
                break;
              case 'email_settings':
                mockSettings.email_settings = { ...mockSettings.email_settings, ...value };
                break;
              case 'security_settings':
                mockSettings.security_settings = { ...mockSettings.security_settings, ...value };
                break;
            }
          } catch (e) {
            console.warn('Failed to parse setting:', setting.key, setting.value);
          }
        });
      }

      setSettings(mockSettings);
    } catch (err: any) {
      console.error('Failed to fetch settings:', err);
      setError(err.message || 'Failed to load system settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: string, value: any) => {
    try {
      await apiClient.updateSystemSettings({ [key]: value });
      await fetchSettings(); // Refresh settings
    } catch (err: any) {
      console.error('Failed to update setting:', err);
      setError(err.message || 'Failed to update system setting');
    }
  };

  const toggleMaintenanceMode = async () => {
    if (!settings) return;

    const newValue = !settings.maintenance_mode;
    await updateSetting('maintenance_mode', newValue);
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      // In real implementation, save to API
      // await apiClient.updateSystemSettings(settings);
      
      // Mock success
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess('Settings saved successfully');
    } catch (err: any) {
      console.error('Failed to save settings:', err);
      setError(err.message || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <div className="badge badge-success">Healthy</div>;
      case 'warning':
        return <div className="badge badge-warning">Warning</div>;
      case 'error':
        return <div className="badge badge-error">Error</div>;
      default:
        return <div className="badge badge-neutral">Unknown</div>;
    }
  };

  if (currentUser && currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <Shield className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access system settings.</p>
              <a href="/dashboard" className="btn btn-primary">Go to Dashboard</a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !settings) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load Settings</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button 
                className="btn btn-primary"
                onClick={fetchSettings}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">System Settings</h1>
            <p className="opacity-70 mt-2">
              Configure system-wide settings and monitor system health
            </p>
          </div>
          
          <button
            onClick={handleSaveSettings}
            className="btn btn-primary"
            disabled={saving || loading}
          >
            {saving && <span className="loading loading-spinner loading-sm"></span>}
            Save Changes
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            <CheckCircle className="w-5 h-5" />
            <span>{success}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setSuccess(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* System Status */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">System Status</h2>
              {loading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex justify-between">
                      <div className="h-4 bg-base-300 rounded w-1/2"></div>
                      <div className="h-4 bg-base-300 rounded w-1/4"></div>
                    </div>
                  ))}
                </div>
              ) : settings ? (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <Activity className="w-4 h-4" />
                      Version
                    </span>
                    <div className="badge badge-neutral">{settings.system_info.version}</div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <Activity className="w-4 h-4" />
                      Uptime
                    </span>
                    <span className="font-semibold">{settings.system_info.uptime}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      Database
                    </span>
                    {getStatusBadge(settings.system_info.database_status)}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      Redis
                    </span>
                    {getStatusBadge(settings.system_info.redis_status)}
                  </div>
                </div>
              ) : null}
            </div>
          </div>

          {/* Maintenance Mode */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Maintenance Mode</h2>
              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text">Enable maintenance mode</span>
                  <input
                    type="checkbox"
                    checked={settings?.maintenance_mode || false}
                    onChange={toggleMaintenanceMode}
                    className="toggle toggle-primary"
                    disabled={loading}
                  />
                </label>
              </div>
              <p className="text-sm opacity-70">
                When enabled, the API will return maintenance responses to all requests except admin endpoints.
              </p>
            </div>
          </div>
        </div>

        {/* API Rate Limits */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">API Rate Limits</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Requests per minute</span>
                </label>
                <input
                  type="number"
                  value={settings?.api_rate_limits.default_per_minute || 0}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    api_rate_limits: { ...prev.api_rate_limits, default_per_minute: parseInt(e.target.value) }
                  } : null)}
                  className="input input-bordered"
                  disabled={loading}
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Requests per day</span>
                </label>
                <input
                  type="number"
                  value={settings?.api_rate_limits.default_per_day || 0}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    api_rate_limits: { ...prev.api_rate_limits, default_per_day: parseInt(e.target.value) }
                  } : null)}
                  className="input input-bordered"
                  disabled={loading}
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Burst limit</span>
                </label>
                <input
                  type="number"
                  value={settings?.api_rate_limits.burst_limit || 0}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    api_rate_limits: { ...prev.api_rate_limits, burst_limit: parseInt(e.target.value) }
                  } : null)}
                  className="input input-bordered"
                  disabled={loading}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Security Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Minimum password length</span>
                </label>
                <input
                  type="number"
                  min="6"
                  max="32"
                  value={settings?.security_settings.password_min_length || 8}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    security_settings: { ...prev.security_settings, password_min_length: parseInt(e.target.value) }
                  } : null)}
                  className="input input-bordered"
                  disabled={loading}
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Session timeout (minutes)</span>
                </label>
                <input
                  type="number"
                  value={settings?.security_settings.session_timeout_minutes || 1440}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    security_settings: { ...prev.security_settings, session_timeout_minutes: parseInt(e.target.value) }
                  } : null)}
                  className="input input-bordered"
                  disabled={loading}
                />
              </div>
            </div>
            <div className="form-control mt-4">
              <label className="label cursor-pointer">
                <span className="label-text">Require email verification for new accounts</span>
                <input
                  type="checkbox"
                  checked={settings?.security_settings.require_email_verification || false}
                  onChange={(e) => setSettings(prev => prev ? {
                    ...prev,
                    security_settings: { ...prev.security_settings, require_email_verification: e.target.checked }
                  } : null)}
                  className="checkbox"
                  disabled={loading}
                />
              </label>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
