'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Download, 
  FileText, 
  Calendar, 
  Database, 
  Users, 
  CreditCard, 
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

interface ExportJob {
  id: string;
  type: 'analytics' | 'users' | 'subscriptions' | 'api-keys' | 'logs';
  format: 'csv' | 'json';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  file_size?: number;
  download_url?: string;
  parameters: any;
}

interface ExportType {
  id: string;
  name: string;
  description: string;
  icon: any;
  formats: ('csv' | 'json')[];
  parameters?: any[];
}

export default function AdminExportsPage() {
  const { user: currentUser } = useAuthStore();
  const [exports, setExports] = useState<ExportJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [exportTypes] = useState<ExportType[]>([
    {
      id: 'analytics',
      name: 'Analytics Data',
      description: 'Export usage analytics, API calls, and performance metrics',
      icon: Database,
      formats: ['csv', 'json'],
      parameters: [
        { name: 'startDate', type: 'date', label: 'Start Date' },
        { name: 'endDate', type: 'date', label: 'End Date' }
      ]
    },
    {
      id: 'users',
      name: 'User Data',
      description: 'Export user accounts, profiles, and activity data',
      icon: Users,
      formats: ['csv', 'json'],
      parameters: [
        { name: 'includeInactive', type: 'checkbox', label: 'Include Inactive Users' },
        { name: 'includePersonalData', type: 'checkbox', label: 'Include Personal Data' }
      ]
    },
    {
      id: 'subscriptions',
      name: 'Subscription Data',
      description: 'Export subscription plans, billing, and payment history',
      icon: CreditCard,
      formats: ['csv', 'json'],
      parameters: [
        { name: 'status', type: 'select', label: 'Status Filter', options: ['all', 'active', 'canceled'] }
      ]
    },
    {
      id: 'api-keys',
      name: 'API Keys',
      description: 'Export API key usage and statistics',
      icon: FileText,
      formats: ['csv', 'json']
    },
    {
      id: 'logs',
      name: 'System Logs',
      description: 'Export system logs and error reports',
      icon: AlertTriangle,
      formats: ['json'],
      parameters: [
        { name: 'level', type: 'select', label: 'Log Level', options: ['all', 'error', 'warn', 'info'] },
        { name: 'days', type: 'number', label: 'Days Back', default: 7 }
      ]
    }
  ]);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      fetchExports();
    }
  }, [currentUser]);

  const fetchExports = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock export history data
      const mockExports: ExportJob[] = [
        {
          id: '1',
          type: 'analytics',
          format: 'csv',
          status: 'completed',
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000).toISOString(),
          file_size: 2048576,
          download_url: '/exports/analytics-2025-01-12.csv',
          parameters: { startDate: '2025-01-01', endDate: '2025-01-12' }
        },
        {
          id: '2',
          type: 'users',
          format: 'json',
          status: 'processing',
          created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          parameters: { includeInactive: true, includePersonalData: false }
        },
        {
          id: '3',
          type: 'subscriptions',
          format: 'csv',
          status: 'failed',
          created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          parameters: { status: 'active' }
        }
      ];

      setExports(mockExports);
    } catch (err: any) {
      console.error('Failed to fetch exports:', err);
      setError(err.message || 'Failed to load export history');
    } finally {
      setLoading(false);
    }
  };

  const createExport = async (type: string, format: 'csv' | 'json', parameters: any = {}) => {
    try {
      setError(null);
      
      // In a real implementation, this would call the backend API
      const newExport: ExportJob = {
        id: Date.now().toString(),
        type: type as any,
        format,
        status: 'pending',
        created_at: new Date().toISOString(),
        parameters
      };

      setExports(prev => [newExport, ...prev]);
      setSuccess(`Export job created successfully. Processing ${type} data...`);

      // Simulate processing
      setTimeout(() => {
        setExports(prev => prev.map(exp => 
          exp.id === newExport.id 
            ? { ...exp, status: 'processing' as const }
            : exp
        ));
      }, 1000);

      setTimeout(() => {
        setExports(prev => prev.map(exp => 
          exp.id === newExport.id 
            ? { 
                ...exp, 
                status: 'completed' as const,
                completed_at: new Date().toISOString(),
                file_size: Math.floor(Math.random() * 5000000) + 100000,
                download_url: `/exports/${type}-${new Date().toISOString().split('T')[0]}.${format}`
              }
            : exp
        ));
      }, 5000);

    } catch (err: any) {
      console.error('Failed to create export:', err);
      setError(err.message || 'Failed to create export job');
    }
  };

  const downloadExport = async (exportJob: ExportJob) => {
    try {
      if (!exportJob.download_url) return;
      
      // In a real implementation, this would download the actual file
      const link = document.createElement('a');
      link.href = exportJob.download_url;
      link.download = `${exportJob.type}-export-${exportJob.id}.${exportJob.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setSuccess('Export download started');
    } catch (err: any) {
      console.error('Failed to download export:', err);
      setError(err.message || 'Failed to download export');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-success" />;
      case 'processing': return <RefreshCw className="w-4 h-4 text-info animate-spin" />;
      case 'failed': return <AlertTriangle className="w-4 h-4 text-error" />;
      case 'pending': return <Clock className="w-4 h-4 text-warning" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed': return 'badge-success';
      case 'processing': return 'badge-info';
      case 'failed': return 'badge-error';
      case 'pending': return 'badge-warning';
      default: return 'badge-ghost';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access data exports.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Data Export Center</h1>
            <p className="opacity-70">Export system data for analysis and backup purposes</p>
          </div>
          <button
            className="btn btn-primary"
            onClick={fetchExports}
            disabled={loading}
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            <CheckCircle className="w-5 h-5" />
            <span>{success}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setSuccess(null)}
            >
              ×
            </button>
          </div>
        )}

        {/* Export Types */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {exportTypes.map((exportType) => {
            const IconComponent = exportType.icon;
            return (
              <div key={exportType.id} className="card bg-base-100 shadow-xl">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="w-6 h-6 text-primary" />
                    </div>
                    <h2 className="card-title text-lg">{exportType.name}</h2>
                  </div>
                  
                  <p className="text-sm opacity-80 mb-4">{exportType.description}</p>
                  
                  <div className="flex gap-2">
                    {exportType.formats.map((format) => (
                      <button
                        key={format}
                        className="btn btn-sm btn-outline"
                        onClick={() => createExport(exportType.id, format)}
                      >
                        <Download className="w-4 h-4" />
                        {format.toUpperCase()}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Export History */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title mb-4">Export History</h2>
            
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
              </div>
            ) : exports.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-16 h-16 mx-auto opacity-50 mb-4" />
                <h3 className="text-lg font-medium">No Exports Yet</h3>
                <p className="opacity-70">Create your first export using the options above.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Type</th>
                      <th>Format</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Size</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {exports.map((exportJob) => (
                      <tr key={exportJob.id}>
                        <td>
                          <div className="flex items-center gap-2">
                            <span className="capitalize">{exportJob.type.replace('-', ' ')}</span>
                          </div>
                        </td>
                        <td>
                          <div className="badge badge-outline">
                            {exportJob.format.toUpperCase()}
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(exportJob.status)}
                            <div className={`badge ${getStatusBadge(exportJob.status)}`}>
                              {exportJob.status}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            <span className="text-sm">{formatDate(exportJob.created_at)}</span>
                          </div>
                        </td>
                        <td>{formatFileSize(exportJob.file_size)}</td>
                        <td>
                          {exportJob.status === 'completed' && exportJob.download_url && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => downloadExport(exportJob)}
                            >
                              <Download className="w-4 h-4" />
                              Download
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
