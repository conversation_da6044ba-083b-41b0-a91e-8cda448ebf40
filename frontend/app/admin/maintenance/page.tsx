'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Database, 
  Trash2, 
  <PERSON>fresh<PERSON><PERSON>, 
  AlertTriangle, 
  CheckCircle, 
  Settings, 
  HardDrive,
  Zap,
  Clock
} from 'lucide-react';

interface MaintenanceOperation {
  id: string;
  name: string;
  description: string;
  icon: any;
  action: () => Promise<void>;
  loading: boolean;
  lastRun?: string;
  status?: 'success' | 'error' | 'running';
}

export default function AdminMaintenancePage() {
  const { user: currentUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [operations, setOperations] = useState<MaintenanceOperation[]>([]);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      initializeOperations();
    }
  }, [currentUser]);

  const initializeOperations = () => {
    const maintenanceOps: MaintenanceOperation[] = [
      {
        id: 'vacuum',
        name: 'Database Vacuum',
        description: 'Optimize database performance by reclaiming storage space and updating statistics',
        icon: Database,
        action: runDatabaseVacuum,
        loading: false,
        lastRun: localStorage.getItem('maintenance_vacuum_last_run') || undefined
      },
      {
        id: 'cache-clear',
        name: 'Clear System Cache',
        description: 'Remove expired cache entries and free up memory',
        icon: Trash2,
        action: clearSystemCache,
        loading: false,
        lastRun: localStorage.getItem('maintenance_cache_last_run') || undefined
      },
      {
        id: 'optimize-tables',
        name: 'Optimize Database Tables',
        description: 'Analyze and optimize database table structures for better performance',
        icon: HardDrive,
        action: optimizeTables,
        loading: false,
        lastRun: localStorage.getItem('maintenance_optimize_last_run') || undefined
      },
      {
        id: 'cleanup-logs',
        name: 'Cleanup Old Logs',
        description: 'Remove old log entries to free up storage space',
        icon: Zap,
        action: cleanupLogs,
        loading: false,
        lastRun: localStorage.getItem('maintenance_cleanup_last_run') || undefined
      }
    ];

    setOperations(maintenanceOps);
  };

  const updateOperationStatus = (id: string, updates: Partial<MaintenanceOperation>) => {
    setOperations(prev => prev.map(op => 
      op.id === id ? { ...op, ...updates } : op
    ));
  };

  const runDatabaseVacuum = async () => {
    try {
      updateOperationStatus('vacuum', { loading: true, status: 'running' });
      
      const result = await apiClient.runDatabaseVacuum();
      
      const now = new Date().toISOString();
      localStorage.setItem('maintenance_vacuum_last_run', now);
      
      updateOperationStatus('vacuum', { 
        loading: false, 
        status: 'success', 
        lastRun: now 
      });
      
      setSuccess('Database vacuum completed successfully');
    } catch (err: any) {
      console.error('Database vacuum failed:', err);
      updateOperationStatus('vacuum', { loading: false, status: 'error' });
      setError(err.message || 'Failed to run database vacuum');
    }
  };

  const clearSystemCache = async () => {
    try {
      updateOperationStatus('cache-clear', { loading: true, status: 'running' });
      
      const result = await apiClient.clearSystemCache();
      
      const now = new Date().toISOString();
      localStorage.setItem('maintenance_cache_last_run', now);
      
      updateOperationStatus('cache-clear', { 
        loading: false, 
        status: 'success', 
        lastRun: now 
      });
      
      setSuccess(`Cache cleared successfully. Removed ${result.data?.cleared_entries || 0} entries.`);
    } catch (err: any) {
      console.error('Cache clear failed:', err);
      updateOperationStatus('cache-clear', { loading: false, status: 'error' });
      setError(err.message || 'Failed to clear system cache');
    }
  };

  const optimizeTables = async () => {
    try {
      updateOperationStatus('optimize-tables', { loading: true, status: 'running' });
      
      // Simulate optimization process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const now = new Date().toISOString();
      localStorage.setItem('maintenance_optimize_last_run', now);
      
      updateOperationStatus('optimize-tables', { 
        loading: false, 
        status: 'success', 
        lastRun: now 
      });
      
      setSuccess('Database tables optimized successfully');
    } catch (err: any) {
      console.error('Table optimization failed:', err);
      updateOperationStatus('optimize-tables', { loading: false, status: 'error' });
      setError(err.message || 'Failed to optimize database tables');
    }
  };

  const cleanupLogs = async () => {
    try {
      updateOperationStatus('cleanup-logs', { loading: true, status: 'running' });
      
      // Simulate log cleanup process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const now = new Date().toISOString();
      localStorage.setItem('maintenance_cleanup_last_run', now);
      
      updateOperationStatus('cleanup-logs', { 
        loading: false, 
        status: 'success', 
        lastRun: now 
      });
      
      setSuccess('Old logs cleaned up successfully');
    } catch (err: any) {
      console.error('Log cleanup failed:', err);
      updateOperationStatus('cleanup-logs', { loading: false, status: 'error' });
      setError(err.message || 'Failed to cleanup old logs');
    }
  };

  const formatLastRun = (lastRun?: string) => {
    if (!lastRun) return 'Never';
    
    const date = new Date(lastRun);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 30) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-success" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-error" />;
      case 'running': return <RefreshCw className="w-4 h-4 text-info animate-spin" />;
      default: return null;
    }
  };

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access maintenance operations.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">System Maintenance</h1>
            <p className="opacity-70">Perform system maintenance operations and optimizations</p>
          </div>
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            <span className="text-sm opacity-70">Admin Operations</span>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            <CheckCircle className="w-5 h-5" />
            <span>{success}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setSuccess(null)}
            >
              ×
            </button>
          </div>
        )}

        {/* Warning Notice */}
        <div className="alert alert-warning">
          <AlertTriangle className="w-5 h-5" />
          <div>
            <h3 className="font-bold">Caution: Maintenance Operations</h3>
            <div className="text-sm">
              These operations may temporarily affect system performance. 
              It's recommended to run them during low-traffic periods.
            </div>
          </div>
        </div>

        {/* Maintenance Operations */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {operations.map((operation) => {
            const IconComponent = operation.icon;
            return (
              <div key={operation.id} className="card bg-base-100 shadow-xl">
                <div className="card-body">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h2 className="card-title text-lg">{operation.name}</h2>
                      <div className="flex items-center gap-2 text-sm opacity-70">
                        <Clock className="w-3 h-3" />
                        <span>Last run: {formatLastRun(operation.lastRun)}</span>
                        {getStatusIcon(operation.status)}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-sm opacity-80 mb-4">{operation.description}</p>
                  
                  <div className="card-actions justify-end">
                    <button
                      className={`btn btn-primary ${operation.loading ? 'loading' : ''}`}
                      onClick={operation.action}
                      disabled={operation.loading || loading}
                    >
                      {operation.loading ? (
                        <>
                          <span className="loading loading-spinner loading-sm"></span>
                          Running...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4" />
                          Run Operation
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* System Status */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">System Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="stat">
                <div className="stat-figure text-primary">
                  <Database className="w-8 h-8" />
                </div>
                <div className="stat-title">Database</div>
                <div className="stat-value text-primary">Healthy</div>
                <div className="stat-desc">All connections active</div>
              </div>
              
              <div className="stat">
                <div className="stat-figure text-secondary">
                  <HardDrive className="w-8 h-8" />
                </div>
                <div className="stat-title">Storage</div>
                <div className="stat-value text-secondary">85%</div>
                <div className="stat-desc">15% free space remaining</div>
              </div>
              
              <div className="stat">
                <div className="stat-figure text-accent">
                  <Zap className="w-8 h-8" />
                </div>
                <div className="stat-title">Performance</div>
                <div className="stat-value text-accent">Good</div>
                <div className="stat-desc">Response time: 120ms</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
