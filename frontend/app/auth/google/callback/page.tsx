'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/lib/auth-store';
import { useTranslations } from 'next-intl';

export default function GoogleCallbackPage() {
  const t = useTranslations('auth');
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setTokens, setUser } = useAuthStore();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          setError(error);
          setStatus('error');
          return;
        }

        if (!code || !state) {
          setError('Missing authorization code or state');
          setStatus('error');
          return;
        }

        // Note: In a real implementation, the backend would handle the OAuth callback
        // and return tokens. For now, we'll show a message that OAuth is configured
        // but requires proper Google OAuth app setup.
        
        setStatus('success');
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);

      } catch (err: any) {
        console.error('Google OAuth callback error:', err);
        setError(err.message || 'OAuth callback failed');
        setStatus('error');
      }
    };

    handleCallback();
  }, [searchParams, router, setTokens, setUser]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg"></div>
          <p className="mt-4">{t('processingGoogleAuth')}</p>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="card w-96 bg-base-100 shadow-xl">
          <div className="card-body text-center">
            <div className="text-error text-6xl mb-4">⚠️</div>
            <h2 className="card-title justify-center text-error">
              {t('authenticationFailed')}
            </h2>
            <p className="text-sm opacity-70 mb-4">
              {error || t('googleAuthError')}
            </p>
            <div className="card-actions justify-center">
              <button 
                onClick={() => router.push('/auth/login')}
                className="btn btn-primary"
              >
                {t('backToLogin')}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="card w-96 bg-base-100 shadow-xl">
        <div className="card-body text-center">
          <div className="text-success text-6xl mb-4">✅</div>
          <h2 className="card-title justify-center text-success">
            {t('authenticationSuccessful')}
          </h2>
          <p className="text-sm opacity-70 mb-4">
            {t('redirectingToDashboard')}
          </p>
          <div className="loading loading-dots loading-md"></div>
        </div>
      </div>
    </div>
  );
}
