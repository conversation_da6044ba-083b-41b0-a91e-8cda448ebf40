'use client';

import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import Link from 'next/link';
import { useTranslations } from '@/lib/translations';

export default function ForgotPasswordPage() {
  const { t } = useTranslations();
  
  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="btn btn-ghost text-xl font-bold">
            {t('ui.postalApi')}
          </Link>
          <h1 className="text-3xl font-bold mt-4 mb-2">{t('auth.forgotPassword.title')}</h1>
          <p className="text-base-content/70">
            {t('auth.forgotPassword.subtitle')}
          </p>
        </div>

        {/* Forgot Password Form */}
        <ForgotPasswordForm />

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-base-content/70">
            {t('ui.rememberPassword')}{' '}
            <Link href="/auth/login" className="link link-primary">
              {t('ui.signInHere')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}