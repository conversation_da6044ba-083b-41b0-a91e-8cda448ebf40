'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Language = 'en' | 'lt';

interface LanguageState {
  currentLanguage: Language;
  setLanguage: (language: Language) => void;
  _hasHydrated: boolean;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set) => ({
      currentLanguage: 'en',
      setLanguage: (language: Language) => set({ currentLanguage: language }),
      _hasHydrated: false,
    }),
    {
      name: 'language-storage',
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hasHydrated = true;
        }
      },
    }
  )
);