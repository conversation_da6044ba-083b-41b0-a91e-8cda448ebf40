import enMessages from '../messages/en.json';
import ltMessages from '../messages/lt.json';
import { useLanguageStore, type Language } from './language-store';

type Messages = typeof enMessages;
type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

type TranslationKey = NestedKeyOf<Messages>;

const messages = {
  en: enMessages,
  lt: ltMessages,
};

// Server-side function for getting translations with explicit language
export function getTranslations(language: Language = 'en') {
  const t = (key: TranslationKey): string => {
    const keys = key.split('.');
    let value: any = messages[language];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  return { t, currentLanguage: language };
}

// Client-side hook for reactive translations with hydration safety
export function useTranslations() {
  // Always call the hook to maintain hook order consistency
  const { currentLanguage, _hasHydrated } = useLanguageStore();
  
  // During SSR or before hydration, always return English
  if (!_hasHydrated) {
    return getTranslations('en');
  }
  
  // After hydration, use the current language
  return getTranslations(currentLanguage);
}

// Export messages for direct access if needed
export { messages };