/**
 * User Service
 * 
 * Handles all user-related operations including CRUD, authentication,
 * and profile management. Follows the established service patterns.
 */

import { Pool } from 'pg';
import bcrypt from 'bcrypt';
import { getDatabasePool } from '../database/connection';
import {
  User,
  UserProfile,
  CreateUserRequest,
  UpdateUserRequest,
  UserListQuery,
  UserListResponse
} from '../types/auth';

export class UserService {
  private pool: Pool;

  constructor() {
    this.pool = getDatabasePool();
  }

  // =============================================================================
  // USER CRUD OPERATIONS
  // =============================================================================

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserRequest): Promise<User> {
    const {
      email,
      password,
      google_id,
      google_email,
      first_name,
      last_name,
      role = 'CUSTOMER'
    } = userData;

    // Hash password if provided
    let password_hash: string | undefined;
    if (password) {
      password_hash = await bcrypt.hash(password, 12);
    }

    const query = `
      INSERT INTO users (
        email, password_hash, google_id, google_email, 
        first_name, last_name, role, email_verified
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      email,
      password_hash,
      google_id,
      google_email,
      first_name,
      last_name,
      role,
      google_id ? true : false // Auto-verify Google OAuth users
    ];

    const result = await this.pool.query(query, values);
    return result.rows[0];
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE id = $1';
    const result = await this.pool.query(query, [id]);
    return result.rows[0] || null;
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await this.pool.query(query, [email]);
    return result.rows[0] || null;
  }

  /**
   * Get user by Google ID
   */
  async getUserByGoogleId(googleId: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE google_id = $1';
    const result = await this.pool.query(query, [googleId]);
    return result.rows[0] || null;
  }

  /**
   * Update user
   */
  async updateUser(id: string, updates: UpdateUserRequest): Promise<User | null> {
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'password' && value) {
          // Hash password if being updated
          updateFields.push(`password_hash = $${paramIndex}`);
          values.push(bcrypt.hashSync(value, 12));
        } else {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(value);
        }
        paramIndex++;
      } else if (value === null) {
        // Explicitly handle null values for fields that can be nullable
        if (['first_name', 'last_name', 'google_id', 'google_email'].includes(key)) {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(null);
          paramIndex++;
        }
      }
    });

    if (updateFields.length === 0) {
      return this.getUserById(id);
    }

    values.push(id);
    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await this.pool.query(query, values);
    return result.rows[0] || null;
  }

  /**
   * Delete user (soft delete by deactivating)
   */
  async deleteUser(id: string): Promise<boolean> {
    const query = `
      UPDATE users 
      SET is_active = FALSE, updated_at = NOW()
      WHERE id = $1
    `;
    
    const result = await this.pool.query(query, [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Hard delete user (for GDPR compliance)
   */
  async hardDeleteUser(id: string): Promise<boolean> {
    const query = 'DELETE FROM users WHERE id = $1';
    const result = await this.pool.query(query, [id]);
    return (result.rowCount || 0) > 0;
  }

  // =============================================================================
  // USER LISTING AND SEARCH
  // =============================================================================

  /**
   * Get paginated list of users with filtering
   */
  async listUsers(query: UserListQuery): Promise<UserListResponse> {
    const {
      page = 1,
      limit = 20,
      role,
      is_active,
      email_verified,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = query;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (role) {
      conditions.push(`role = $${paramIndex}`);
      values.push(role);
      paramIndex++;
    }

    if (is_active !== undefined) {
      conditions.push(`is_active = $${paramIndex}`);
      values.push(is_active);
      paramIndex++;
    }

    if (email_verified !== undefined) {
      conditions.push(`email_verified = $${paramIndex}`);
      values.push(email_verified);
      paramIndex++;
    }

    if (search) {
      conditions.push(`(
        email ILIKE $${paramIndex} OR 
        first_name ILIKE $${paramIndex} OR 
        last_name ILIKE $${paramIndex} OR
        CONCAT(first_name, ' ', last_name) ILIKE $${paramIndex}
      )`);
      values.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users
      ${whereClause}
    `;
    const countResult = await this.pool.query(countQuery, values);
    const total = parseInt(countResult.rows[0].total);

    // Get users
    const usersQuery = `
      SELECT 
        id, email, first_name, last_name, role, 
        is_active, email_verified, created_at, 
        updated_at, last_login_at
      FROM users
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    values.push(limit, offset);
    const usersResult = await this.pool.query(usersQuery, values);

    const totalPages = Math.ceil(total / limit);

    return {
      users: usersResult.rows.map(this.mapToUserProfile),
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // =============================================================================
  // AUTHENTICATION HELPERS
  // =============================================================================

  /**
   * Verify user password
   */
  async verifyPassword(email: string, password: string): Promise<User | null> {
    const user = await this.getUserByEmail(email);
    
    if (!user || !user.password_hash) {
      return null;
    }

    const isValid = await bcrypt.compare(password, user.password_hash);
    return isValid ? user : null;
  }

  /**
   * Update user password
   */
  async updatePassword(userId: string, newPassword: string): Promise<void> {
    const passwordHash = await bcrypt.hash(newPassword, 12);
    const query = `
      UPDATE users
      SET password_hash = $1, updated_at = NOW()
      WHERE id = $2
    `;
    await this.pool.query(query, [passwordHash, userId]);
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id: string): Promise<void> {
    const query = `
      UPDATE users
      SET last_login_at = NOW(), updated_at = NOW()
      WHERE id = $1
    `;
    await this.pool.query(query, [id]);
  }

  /**
   * Verify user email
   */
  async verifyEmail(id: string): Promise<boolean> {
    const query = `
      UPDATE users 
      SET email_verified = TRUE, updated_at = NOW()
      WHERE id = $1
    `;
    const result = await this.pool.query(query, [id]);
    return (result.rowCount || 0) > 0;
  }

  // =============================================================================
  // ADMIN OPERATIONS
  // =============================================================================

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    admins: number;
    customers: number;
    recent: number;
  }> {
    const query = `
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_active = TRUE) as active,
        COUNT(*) FILTER (WHERE email_verified = TRUE) as verified,
        COUNT(*) FILTER (WHERE role = 'ADMIN') as admins,
        COUNT(*) FILTER (WHERE role = 'CUSTOMER') as customers,
        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as recent
      FROM users
    `;

    const result = await this.pool.query(query);
    const stats = result.rows[0];

    return {
      total: parseInt(stats.total),
      active: parseInt(stats.active),
      verified: parseInt(stats.verified),
      admins: parseInt(stats.admins),
      customers: parseInt(stats.customers),
      recent: parseInt(stats.recent)
    };
  }

  /**
   * Bulk update users
   */
  async bulkUpdateUsers(userIds: string[], updates: Partial<UpdateUserRequest>): Promise<number> {
    if (userIds.length === 0) return 0;

    const updateFields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build update fields
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (updateFields.length === 0) return 0;

    values.push(userIds);
    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE id = ANY($${paramIndex})
    `;

    const result = await this.pool.query(query, values);
    return result.rowCount || 0;
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Check if email exists
   */
  async emailExists(email: string, excludeId?: string): Promise<boolean> {
    let query = 'SELECT id FROM users WHERE email = $1';
    const values: any[] = [email];

    if (excludeId) {
      query += ' AND id != $2';
      values.push(excludeId);
    }

    const result = await this.pool.query(query, values);
    return result.rows.length > 0;
  }

  /**
   * Map database user to profile (removes sensitive data)
   */
  private mapToUserProfile(user: User): UserProfile {
    return {
      id: user.id,
      email: user.email,
      first_name: user.first_name || null,
      last_name: user.last_name || null,
      role: user.role,
      is_active: user.is_active,
      email_verified: user.email_verified,
      created_at: user.created_at,
      last_login_at: user.last_login_at || null
    };
  }

  /**
   * Get user profile (safe for API responses)
   */
  async getUserProfile(id: string): Promise<UserProfile | null> {
    const user = await this.getUserById(id);
    return user ? this.mapToUserProfile(user) : null;
  }

  // =============================================================================
  // ADMIN-SPECIFIC METHODS
  // =============================================================================

  /**
   * Get users with advanced filtering for admin
   */
  async getUsers(options: {
    page: number;
    limit: number;
    role?: 'ADMIN' | 'CUSTOMER';
    isActive?: boolean;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    createdAfter?: Date;
    createdBefore?: Date;
    lastLoginAfter?: Date;
    lastLoginBefore?: Date;
  }): Promise<{
    users: User[];
    pagination: any;
    summary?: any;
  }> {
    const {
      page,
      limit,
      role,
      isActive,
      search,
      sortBy = 'created_at',
      sortOrder = 'desc',
      createdAfter,
      createdBefore,
      lastLoginAfter,
      lastLoginBefore
    } = options;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (role) {
      conditions.push(`role = $${paramIndex++}`);
      params.push(role);
    }

    if (isActive !== undefined) {
      conditions.push(`is_active = $${paramIndex++}`);
      params.push(isActive);
    }

    if (search) {
      conditions.push(`(email ILIKE $${paramIndex++})`);
      const searchPattern = `%${search}%`;
      params.push(searchPattern);
    }

    if (createdAfter) {
      conditions.push(`created_at >= $${paramIndex++}`);
      params.push(createdAfter);
    }

    if (createdBefore) {
      conditions.push(`created_at <= $${paramIndex++}`);
      params.push(createdBefore);
    }

    if (lastLoginAfter) {
      conditions.push(`last_login_at >= $${paramIndex++}`);
      params.push(lastLoginAfter);
    }

    if (lastLoginBefore) {
      conditions.push(`last_login_at <= $${paramIndex++}`);
      params.push(lastLoginBefore);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM users ${whereClause}`;
    const countResult = await this.pool.query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get users
    const usersQuery = `
      SELECT * FROM users
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;
    params.push(limit, offset);

    const usersResult = await this.pool.query(usersQuery, params);

    // Get summary statistics
    const summaryQuery = `
      SELECT
        COUNT(*) FILTER (WHERE is_active = true) as active_users,
        COUNT(*) FILTER (WHERE is_active = false) as inactive_users,
        COUNT(*) FILTER (WHERE role = 'ADMIN') as admin_users,
        COUNT(*) FILTER (WHERE role = 'CUSTOMER') as customer_users
      FROM users
      ${whereClause}
    `;
    const summaryResult = await this.pool.query(summaryQuery, params.slice(0, -2));

    return {
      users: usersResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      summary: summaryResult.rows[0]
    };
  }

  /**
   * Get user subscriptions
   */
  async getUserSubscriptions(userId: string): Promise<any[]> {
    const query = `
      SELECT s.*, sp.name as plan_name, sp.price_eur, sp.currency
      FROM user_subscriptions s
      JOIN subscription_plans sp ON s.plan_id = sp.id
      WHERE s.user_id = $1
      ORDER BY s.created_at DESC
    `;
    const result = await this.pool.query(query, [userId]);
    return result.rows;
  }

  /**
   * Get user API keys
   */
  async getUserApiKeys(userId: string): Promise<any[]> {
    const query = `
      SELECT id, name, description, is_active, created_at, last_used_at, total_requests
      FROM api_keys
      WHERE user_id = $1
      ORDER BY created_at DESC
    `;
    const result = await this.pool.query(query, [userId]);
    return result.rows;
  }

  /**
   * Get user usage statistics
   */
  async getUserUsageStats(userId: string): Promise<any> {
    const query = `
      SELECT
        SUM(total_requests) as total_requests,
        SUM(requests_this_month) as requests_this_month,
        MAX(last_used_at) as last_api_usage
      FROM api_keys
      WHERE user_id = $1 AND is_active = true
    `;
    const result = await this.pool.query(query, [userId]);
    return result.rows[0] || { total_requests: 0, requests_this_month: 0, last_api_usage: null };
  }

  /**
   * Suspend user account
   */
  async suspendUser(userId: string, reason: string, notes?: string, adminId?: string): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Update user status
      await client.query(
        'UPDATE users SET is_active = false, updated_at = NOW() WHERE id = $1',
        [userId]
      );

      // Log the action (if audit table exists)
      try {
        await client.query(`
          INSERT INTO user_audit_log (user_id, action, reason, notes, admin_id, created_at)
          VALUES ($1, 'suspended', $2, $3, $4, NOW())
        `, [userId, reason, notes, adminId]);
      } catch (auditError) {
        // Audit table might not exist, continue without logging
        console.warn('Could not log user suspension to audit table:', auditError);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Activate user account
   */
  async activateUser(userId: string, notes?: string, adminId?: string): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Update user status
      await client.query(
        'UPDATE users SET is_active = true, updated_at = NOW() WHERE id = $1',
        [userId]
      );

      // Log the action (if audit table exists)
      try {
        await client.query(`
          INSERT INTO user_audit_log (user_id, action, reason, notes, admin_id, created_at)
          VALUES ($1, 'activated', 'Admin activation', $2, $3, NOW())
        `, [userId, notes, adminId]);
      } catch (auditError) {
        // Audit table might not exist, continue without logging
        console.warn('Could not log user activation to audit table:', auditError);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Bulk user operations
   */
  async bulkUserAction(
    action: 'suspend' | 'activate' | 'delete' | 'change_role',
    userIds: string[],
    options: {
      reason: string;
      notes?: string;
      adminId: string;
      newRole?: 'ADMIN' | 'CUSTOMER';
    }
  ): Promise<{ success: number; failed: number; errors: any[] }> {
    const results = { success: 0, failed: 0, errors: [] as any[] };

    for (const userId of userIds) {
      try {
        switch (action) {
          case 'suspend':
            await this.suspendUser(userId, options.reason, options.notes, options.adminId);
            break;
          case 'activate':
            await this.activateUser(userId, options.notes, options.adminId);
            break;
          case 'delete':
            await this.deleteUser(userId);
            break;
          case 'change_role':
            if (options.newRole) {
              await this.updateUser(userId, { role: options.newRole });
            }
            break;
        }
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({ userId, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }

    return results;
  }

  /**
   * Get user statistics for admin dashboard
   */
  async getUserStatistics(): Promise<any> {
    const query = `
      SELECT
        COUNT(*) as total_users,
        COUNT(*) FILTER (WHERE is_active = true) as active_users,
        COUNT(*) FILTER (WHERE is_active = false) as inactive_users,
        COUNT(*) FILTER (WHERE role = 'ADMIN') as admin_users,
        COUNT(*) FILTER (WHERE role = 'CUSTOMER') as customer_users,
        COUNT(*) FILTER (WHERE email_verified = true) as verified_users,
        COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
        COUNT(*) FILTER (WHERE last_login_at >= CURRENT_DATE - INTERVAL '30 days') as active_users_30d
      FROM users
    `;
    const result = await this.pool.query(query);
    return result.rows[0];
  }
}
