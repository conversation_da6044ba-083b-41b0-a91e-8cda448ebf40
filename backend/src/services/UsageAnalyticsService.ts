/**
 * Usage Analytics Service
 * 
 * Comprehensive service for aggregating, analyzing, and reporting API usage data.
 * Provides insights into usage patterns, performance metrics, and system health.
 */

import { Pool } from 'pg';
import { getDatabasePool } from '../database/connection';

// Types for usage analytics
export interface UsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  errorRate: number;
  dataTransferred: number;
}

export interface EndpointStats {
  endpoint: string;
  requests: number;
  avgResponseTime: number;
  errorRate: number;
}

export interface TimeSeriesData {
  timestamp: string;
  requests: number;
  avgResponseTime: number;
  errorRate: number;
}

export interface UsageAlert {
  id: string;
  userId: string;
  apiKeyId?: string;
  alertType: string;
  thresholdType: string;
  thresholdValue: number;
  currentValue: number;
  message: string;
  severity: 'info' | 'warning' | 'critical';
  isResolved: boolean;
  createdAt: string;
  resolvedAt?: string;
}

export interface UsageQuota {
  id: string;
  userId: string;
  quotaPeriod: 'daily' | 'monthly' | 'yearly';
  requestsLimit: number;
  requestsUsed: number;
  dataTransferLimit: number;
  dataTransferUsed: number;
  isExceeded: boolean;
  periodStart: string;
  periodEnd: string;
}

export interface AnalyticsFilters {
  userId?: string | undefined;
  apiKeyId?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  endpoint?: string | undefined;
  statusCode?: number | undefined;
}

export class UsageAnalyticsService {
  private pool: Pool | null = null;

  private getPool(): Pool {
    if (!this.pool) {
      this.pool = getDatabasePool();
    }
    return this.pool;
  }

  /**
   * Get usage statistics for a specific period
   */
  async getUsageStats(filters: AnalyticsFilters): Promise<UsageStats> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (filters.userId) {
      whereClause += ` AND user_id = $${paramIndex}`;
      params.push(filters.userId);
      paramIndex++;
    }

    if (filters.apiKeyId) {
      whereClause += ` AND api_key_id = $${paramIndex}`;
      params.push(filters.apiKeyId);
      paramIndex++;
    }

    if (filters.startDate) {
      whereClause += ` AND created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      whereClause += ` AND created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    if (filters.endpoint) {
      whereClause += ` AND endpoint = $${paramIndex}`;
      params.push(filters.endpoint);
      paramIndex++;
    }

    if (filters.statusCode) {
      whereClause += ` AND status_code = $${paramIndex}`;
      params.push(filters.statusCode);
      paramIndex++;
    }

    const query = `
      SELECT 
        COUNT(*) as total_requests,
        COUNT(CASE WHEN status_code < 400 THEN 1 END) as successful_requests,
        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as failed_requests,
        AVG(response_time_ms) as avg_response_time,
        (COUNT(CASE WHEN status_code >= 400 THEN 1 END)::float / NULLIF(COUNT(*), 0)) * 100 as error_rate,
        SUM(request_size_bytes + response_size_bytes) as data_transferred
      FROM api_request_analytics
      ${whereClause}
    `;

    const result = await pool.query(query, params);
    const row = result.rows[0];

    return {
      totalRequests: parseInt(row.total_requests) || 0,
      successfulRequests: parseInt(row.successful_requests) || 0,
      failedRequests: parseInt(row.failed_requests) || 0,
      avgResponseTime: parseFloat(row.avg_response_time) || 0,
      errorRate: parseFloat(row.error_rate) || 0,
      dataTransferred: parseInt(row.data_transferred) || 0
    };
  }

  /**
   * Get top endpoints by usage
   */
  async getTopEndpoints(filters: AnalyticsFilters, limit: number = 10): Promise<EndpointStats[]> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (filters.userId) {
      whereClause += ` AND user_id = $${paramIndex}`;
      params.push(filters.userId);
      paramIndex++;
    }

    if (filters.startDate) {
      whereClause += ` AND created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      whereClause += ` AND created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    params.push(limit);

    const query = `
      SELECT 
        endpoint,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time,
        (COUNT(CASE WHEN status_code >= 400 THEN 1 END)::float / NULLIF(COUNT(*), 0)) * 100 as error_rate
      FROM api_request_analytics
      ${whereClause}
      GROUP BY endpoint
      ORDER BY requests DESC
      LIMIT $${paramIndex}
    `;

    const result = await pool.query(query, params);

    return result.rows.map(row => ({
      endpoint: row.endpoint,
      requests: parseInt(row.requests),
      avgResponseTime: parseFloat(row.avg_response_time) || 0,
      errorRate: parseFloat(row.error_rate) || 0
    }));
  }

  /**
   * Get time series data for usage trends
   */
  async getTimeSeriesData(
    filters: AnalyticsFilters, 
    interval: 'hour' | 'day' | 'week' = 'day'
  ): Promise<TimeSeriesData[]> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (filters.userId) {
      whereClause += ` AND user_id = $${paramIndex}`;
      params.push(filters.userId);
      paramIndex++;
    }

    if (filters.startDate) {
      whereClause += ` AND created_at >= $${paramIndex}`;
      params.push(filters.startDate);
      paramIndex++;
    }

    if (filters.endDate) {
      whereClause += ` AND created_at <= $${paramIndex}`;
      params.push(filters.endDate);
      paramIndex++;
    }

    const truncFunction = interval === 'hour' ? 'hour' : interval === 'day' ? 'day' : 'week';

    const query = `
      SELECT 
        date_trunc('${truncFunction}', created_at) as timestamp,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time,
        (COUNT(CASE WHEN status_code >= 400 THEN 1 END)::float / NULLIF(COUNT(*), 0)) * 100 as error_rate
      FROM api_request_analytics
      ${whereClause}
      GROUP BY date_trunc('${truncFunction}', created_at)
      ORDER BY timestamp
    `;

    const result = await pool.query(query, params);

    return result.rows.map(row => ({
      timestamp: row.timestamp.toISOString(),
      requests: parseInt(row.requests),
      avgResponseTime: parseFloat(row.avg_response_time) || 0,
      errorRate: parseFloat(row.error_rate) || 0
    }));
  }

  /**
   * Get usage aggregations from pre-computed data
   */
  async getUsageAggregations(
    userId?: string,
    apiKeyId?: string,
    periodType: 'minute' | 'hour' | 'day' | 'month' = 'day',
    limit: number = 30
  ): Promise<any[]> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE period_type = $1';
    const params: any[] = [periodType];
    let paramIndex = 2;

    if (userId) {
      whereClause += ` AND user_id = $${paramIndex}`;
      params.push(userId);
      paramIndex++;
    }

    if (apiKeyId) {
      whereClause += ` AND api_key_id = $${paramIndex}`;
      params.push(apiKeyId);
      paramIndex++;
    }

    params.push(limit);

    const query = `
      SELECT 
        period_start,
        period_end,
        total_requests,
        successful_requests,
        failed_requests,
        avg_response_time_ms,
        total_data_transferred_bytes,
        endpoint_stats,
        status_code_stats
      FROM usage_aggregations
      ${whereClause}
      ORDER BY period_start DESC
      LIMIT $${paramIndex}
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Get usage alerts for a user
   */
  async getUsageAlerts(userId: string, includeResolved: boolean = false): Promise<UsageAlert[]> {
    const pool = this.getPool();
    
    let whereClause = 'WHERE user_id = $1';
    const params: any[] = [userId];

    if (!includeResolved) {
      whereClause += ' AND is_resolved = false';
    }

    const query = `
      SELECT 
        id, user_id, api_key_id, alert_type, threshold_type,
        threshold_value, current_value, message, severity,
        is_resolved, created_at, resolved_at
      FROM usage_alerts
      ${whereClause}
      ORDER BY created_at DESC
    `;

    const result = await pool.query(query, params);

    return result.rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      apiKeyId: row.api_key_id,
      alertType: row.alert_type,
      thresholdType: row.threshold_type,
      thresholdValue: row.threshold_value,
      currentValue: row.current_value,
      message: row.message,
      severity: row.severity,
      isResolved: row.is_resolved,
      createdAt: row.created_at.toISOString(),
      resolvedAt: row.resolved_at?.toISOString()
    }));
  }

  /**
   * Get usage quotas for a user
   */
  async getUsageQuotas(userId: string): Promise<UsageQuota[]> {
    const pool = this.getPool();
    
    const query = `
      SELECT 
        id, user_id, quota_period, requests_limit, requests_used,
        data_transfer_limit_bytes, data_transfer_used_bytes,
        is_exceeded, period_start, period_end
      FROM usage_quotas
      WHERE user_id = $1
      ORDER BY period_start DESC
    `;

    const result = await pool.query(query, [userId]);

    return result.rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      quotaPeriod: row.quota_period,
      requestsLimit: row.requests_limit,
      requestsUsed: row.requests_used,
      dataTransferLimit: row.data_transfer_limit_bytes,
      dataTransferUsed: row.data_transfer_used_bytes,
      isExceeded: row.is_exceeded,
      periodStart: row.period_start.toISOString(),
      periodEnd: row.period_end.toISOString()
    }));
  }

  /**
   * Create a usage alert
   */
  async createUsageAlert(
    userId: string,
    apiKeyId: string | null,
    alertType: string,
    thresholdType: string,
    thresholdValue: number,
    currentValue: number,
    message: string,
    severity: 'info' | 'warning' | 'critical' = 'warning'
  ): Promise<string> {
    const pool = this.getPool();

    const query = `
      INSERT INTO usage_alerts (
        user_id, api_key_id, alert_type, threshold_type,
        threshold_value, current_value, message, severity
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id
    `;

    const result = await pool.query(query, [
      userId, apiKeyId, alertType, thresholdType,
      thresholdValue, currentValue, message, severity
    ]);

    return result.rows[0].id;
  }

  /**
   * Resolve a usage alert
   */
  async resolveUsageAlert(alertId: string): Promise<void> {
    const pool = this.getPool();

    const query = `
      UPDATE usage_alerts
      SET is_resolved = true, resolved_at = NOW()
      WHERE id = $1
    `;

    await pool.query(query, [alertId]);
  }

  /**
   * Get system overview for admin dashboard
   */
  async getSystemOverview(): Promise<any> {
    const pool = this.getPool();

    // Get basic system stats
    const statsQuery = `
      SELECT
        COUNT(DISTINCT user_id) as total_users,
        COUNT(DISTINCT api_key_id) as total_api_keys,
        SUM(request_count) as total_requests,
        AVG(response_time_ms) as avg_response_time,
        COUNT(*) FILTER (WHERE status_code >= 400) as error_count,
        COUNT(*) as total_records
      FROM api_usage_logs
      WHERE created_at >= NOW() - INTERVAL '30 days'
    `;

    const statsResult = await pool.query(statsQuery);
    const stats = statsResult.rows[0];

    // Get top endpoints
    const endpointsQuery = `
      SELECT
        endpoint,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time,
        COUNT(*) FILTER (WHERE status_code >= 400) * 100.0 / COUNT(*) as error_rate
      FROM api_usage_logs
      WHERE created_at >= NOW() - INTERVAL '7 days'
      GROUP BY endpoint
      ORDER BY requests DESC
      LIMIT 10
    `;

    const endpointsResult = await pool.query(endpointsQuery);

    return {
      stats: {
        total_users: parseInt(stats.total_users) || 0,
        total_api_keys: parseInt(stats.total_api_keys) || 0,
        total_requests: parseInt(stats.total_requests) || 0,
        avg_response_time: parseFloat(stats.avg_response_time) || 0,
        error_rate: stats.total_records > 0 ? (parseInt(stats.error_count) / parseInt(stats.total_records)) * 100 : 0
      },
      top_endpoints: endpointsResult.rows,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get system-wide performance metrics
   */
  async getSystemPerformanceMetrics(
    startDate?: string,
    endDate?: string
  ): Promise<any> {
    const pool = this.getPool();

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;

    if (startDate) {
      whereClause += ` AND period_start >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereClause += ` AND period_end <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    const query = `
      SELECT
        period_start,
        period_end,
        total_requests,
        avg_response_time_ms,
        p95_response_time_ms,
        p99_response_time_ms,
        error_rate,
        active_users,
        active_api_keys,
        top_endpoints,
        error_breakdown
      FROM system_performance_metrics
      ${whereClause}
      ORDER BY period_start DESC
      LIMIT 100
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Check and create usage threshold alerts
   */
  async checkUsageThresholds(userId: string, apiKeyId?: string): Promise<void> {
    // Get current usage quotas
    const quotas = await this.getUsageQuotas(userId);

    for (const quota of quotas) {
      const usagePercentage = (quota.requestsUsed / quota.requestsLimit) * 100;

      // Check for 80% threshold
      if (usagePercentage >= 80 && usagePercentage < 95) {
        await this.createUsageAlert(
          userId,
          apiKeyId || null,
          'usage_threshold',
          `requests_per_${  quota.quotaPeriod}`,
          quota.requestsLimit,
          quota.requestsUsed,
          `You have used ${usagePercentage.toFixed(1)}% of your ${quota.quotaPeriod} API request quota.`,
          'warning'
        );
      }

      // Check for 95% threshold
      if (usagePercentage >= 95 && !quota.isExceeded) {
        await this.createUsageAlert(
          userId,
          apiKeyId || null,
          'usage_threshold',
          `requests_per_${  quota.quotaPeriod}`,
          quota.requestsLimit,
          quota.requestsUsed,
          `Critical: You have used ${usagePercentage.toFixed(1)}% of your ${quota.quotaPeriod} API request quota.`,
          'critical'
        );
      }

      // Check for quota exceeded
      if (quota.isExceeded) {
        await this.createUsageAlert(
          userId,
          apiKeyId || null,
          'quota_exceeded',
          `requests_per_${  quota.quotaPeriod}`,
          quota.requestsLimit,
          quota.requestsUsed,
          `Your ${quota.quotaPeriod} API request quota has been exceeded. Please upgrade your plan or wait for the next period.`,
          'critical'
        );
      }
    }
  }

  /**
   * Get usage comparison between periods
   */
  async getUsageComparison(
    userId: string,
    currentPeriodStart: string,
    currentPeriodEnd: string,
    previousPeriodStart: string,
    previousPeriodEnd: string
  ): Promise<{
    current: UsageStats;
    previous: UsageStats;
    growth: {
      requests: number;
      avgResponseTime: number;
      errorRate: number;
    };
  }> {
    const currentStats = await this.getUsageStats({
      userId,
      startDate: currentPeriodStart,
      endDate: currentPeriodEnd
    });

    const previousStats = await this.getUsageStats({
      userId,
      startDate: previousPeriodStart,
      endDate: previousPeriodEnd
    });

    const requestsGrowth = previousStats.totalRequests > 0
      ? ((currentStats.totalRequests - previousStats.totalRequests) / previousStats.totalRequests) * 100
      : 0;

    const responseTimeGrowth = previousStats.avgResponseTime > 0
      ? ((currentStats.avgResponseTime - previousStats.avgResponseTime) / previousStats.avgResponseTime) * 100
      : 0;

    const errorRateGrowth = currentStats.errorRate - previousStats.errorRate;

    return {
      current: currentStats,
      previous: previousStats,
      growth: {
        requests: requestsGrowth,
        avgResponseTime: responseTimeGrowth,
        errorRate: errorRateGrowth
      }
    };
  }

  /**
   * Get real-time dashboard data
   */
  async getDashboardData(userId: string): Promise<{
    todayStats: UsageStats;
    quotas: UsageQuota[];
    alerts: UsageAlert[];
    topEndpoints: EndpointStats[];
    recentActivity: TimeSeriesData[];
  }> {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();

    const [todayStats, quotas, alerts, topEndpoints, recentActivity] = await Promise.all([
      this.getUsageStats({ userId, startDate: todayStart, endDate: todayEnd }),
      this.getUsageQuotas(userId),
      this.getUsageAlerts(userId),
      this.getTopEndpoints({ userId, startDate: todayStart, endDate: todayEnd }, 5),
      this.getTimeSeriesData({
        userId,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString()
      }, 'hour')
    ]);

    return {
      todayStats,
      quotas,
      alerts,
      topEndpoints,
      recentActivity
    };
  }
}
