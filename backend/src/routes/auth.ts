/**
 * Authentication Routes
 * 
 * Handles user registration, login, logout, token refresh, and password management.
 * Implements the authentication endpoints specified in the SaaS implementation plan.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/UserService';
import { JWTService } from '../services/JWTService';
import { EmailService } from '../services/EmailService';
import * as crypto from 'crypto';
// Schemas are now defined inline as JSON schemas
import { 
  LoginRequest,
  RegisterRequest,
  RefreshTokenRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  EmailVerificationRequest,
  ResendVerificationRequest
} from '../types/auth';

// Helper functions for service instantiation
const getUserService = () => new UserService();
const getJWTService = () => new JWTService();

// Check if Google OAuth is configured
function isGoogleOAuthConfigured(): boolean {
  const googleClientId = process.env['GOOGLE_CLIENT_ID'];
  const googleClientSecret = process.env['GOOGLE_CLIENT_SECRET'];

  if (!googleClientId || !googleClientSecret ||
      googleClientId.includes('your-google-client-id') ||
      googleClientSecret.includes('your-google-client-secret')) {
    return false;
  }

  return true;
}

export async function authRoutes(fastify: FastifyInstance) {

  // =============================================================================
  // GOOGLE OAUTH ROUTES
  // =============================================================================

  /**
   * GET /auth/google
   * Initiate Google OAuth flow
   */
  fastify.get('/google', async (request: FastifyRequest, reply: FastifyReply) => {
    if (!isGoogleOAuthConfigured()) {
      return reply.status(501).send({
        error: 'OAUTH_NOT_CONFIGURED',
        message: 'Google OAuth is not configured on this server',
        requestId: request.id
      });
    }

    const googleClientId = process.env['GOOGLE_CLIENT_ID'];
    const callbackUrl = process.env['GOOGLE_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/google/callback';

    // Generate state parameter for security
    const state = crypto.randomUUID();

    // Store state in session or return it for client to handle
    const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
    authUrl.searchParams.set('client_id', googleClientId!);
    authUrl.searchParams.set('redirect_uri', callbackUrl);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', 'openid profile email');
    authUrl.searchParams.set('state', state);

    // For development, return the URL instead of redirecting
    return reply.send({
      authUrl: authUrl.toString(),
      state,
      message: 'Visit the authUrl to complete Google OAuth authentication'
    });
  });

  /**
   * GET /auth/google/callback
   * Handle Google OAuth callback
   */
  fastify.get('/google/callback', async (request: FastifyRequest, reply: FastifyReply) => {
    if (!isGoogleOAuthConfigured()) {
      return reply.status(501).send({
        error: 'OAUTH_NOT_CONFIGURED',
        message: 'Google OAuth is not configured on this server',
        requestId: request.id
      });
    }

    return reply.status(501).send({
      error: 'OAUTH_CALLBACK_NOT_IMPLEMENTED',
      message: 'Google OAuth callback requires proper Google OAuth app configuration',
      requestId: request.id,
      note: 'This endpoint would handle the OAuth callback in a production environment'
    });
  });

  // =============================================================================
  // REGISTRATION & LOGIN
  // =============================================================================

  /**
   * POST /auth/register
   * User registration with email and password
   */
  fastify.post<{ Body: RegisterRequest }>('/register', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password', 'confirmPassword'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          confirmPassword: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            user: { type: 'object' },
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RegisterRequest }>, reply: FastifyReply) => {
    try {
      const { email, password, first_name, last_name } = request.body;

      // Check if user already exists
      const existingUser = await getUserService().getUserByEmail(email);
      if (existingUser) {
        return reply.status(400).send({
          error: 'EMAIL_ALREADY_EXISTS',
          message: 'An account with this email already exists',
          requestId: request.id
        });
      }

      // Create new user
      const user = await getUserService().createUser({
        email,
        password,
        first_name: first_name || null,
        last_name: last_name || null,
        role: 'CUSTOMER'
      });

      // Generate auth tokens
      const authResponse = await getJWTService().generateAuthResponse(
        {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at,
          last_login_at: user.last_login_at || null
        },
        request.headers['user-agent'],
        request.ip
      );

      // Send email verification email
      try {
        const emailService = new EmailService();
        await emailService.sendEmailVerification(user.id, user.email, user.first_name);
      } catch (emailError) {
        // Log email error but don't fail registration
        request.log.error('Failed to send verification email:', emailError);
      }

      request.log.info(`User registered: ${user.email}`);

      // Manually construct response to ensure proper serialization
      const response = {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at,
          last_login_at: user.last_login_at || null
        },
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresIn: authResponse.expiresIn
      };

      return reply.status(201).send(response);

    } catch (error) {
      request.log.error('Registration error:', error);
      return reply.status(500).send({
        error: 'REGISTRATION_FAILED',
        message: 'Registration failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/login
   * User login with email and password
   */
  fastify.post<{ Body: LoginRequest }>('/login', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            user: { type: 'object' },
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        401: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: LoginRequest }>, reply: FastifyReply) => {
    try {
      console.log('🔐 LOGIN ENDPOINT HIT:', request.body);
      const { email, password } = request.body;

      // Verify credentials
      const user = await getUserService().verifyPassword(email, password);
      if (!user) {
        return reply.status(401).send({
          error: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password',
          requestId: request.id
        });
      }

      // Check if user is active
      if (!user.is_active) {
        return reply.status(401).send({
          error: 'USER_INACTIVE',
          message: 'Your account has been deactivated. Please contact support.',
          requestId: request.id
        });
      }

      // Update last login
      await getUserService().updateLastLogin(user.id);

      // Generate auth tokens
      const authResponse = await getJWTService().generateAuthResponse(
        {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at,
          last_login_at: user.last_login_at || null
        },
        request.headers['user-agent'],
        request.ip
      );

      request.log.info(`User logged in: ${user.email}`);

      // Manually construct response to ensure proper serialization
      const response = {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at,
          last_login_at: user.last_login_at || null
        },
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresIn: authResponse.expiresIn
      };

      return reply.send(response);

    } catch (error) {
      request.log.error('Login error:', error);
      return reply.status(500).send({
        error: 'LOGIN_FAILED',
        message: 'Login failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // TOKEN MANAGEMENT
  // =============================================================================

  /**
   * POST /auth/refresh
   * Refresh access token using refresh token
   */
  fastify.post<{ Body: RefreshTokenRequest }>('/refresh', {
    schema: {
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        401: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RefreshTokenRequest }>, reply: FastifyReply) => {
    try {
      const { refreshToken } = request.body;

      const tokenResponse = await getJWTService().refreshAccessToken(
        refreshToken,
        request.headers['user-agent'],
        request.ip
      );

      if (!tokenResponse) {
        return reply.status(401).send({
          error: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token',
          requestId: request.id
        });
      }

      return reply.send(tokenResponse);

    } catch (error) {
      request.log.error('Token refresh error:', error);
      return reply.status(500).send({
        error: 'TOKEN_REFRESH_FAILED',
        message: 'Token refresh failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/logout
   * Logout user and revoke refresh token
   */
  fastify.post<{ Body: RefreshTokenRequest }>('/logout', {
    schema: {
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RefreshTokenRequest }>, reply: FastifyReply) => {
    try {
      const { refreshToken } = request.body;

      await getJWTService().revokeRefreshToken(refreshToken, 'User logout');

      return reply.send({
        success: true,
        message: 'Logged out successfully'
      });

    } catch (error) {
      request.log.error('Logout error:', error);
      return reply.status(500).send({
        error: 'LOGOUT_FAILED',
        message: 'Logout failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // PASSWORD MANAGEMENT
  // =============================================================================

  /**
   * POST /auth/forgot-password
   * Request password reset
   */
  fastify.post<{ Body: ForgotPasswordRequest }>('/forgot-password', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ForgotPasswordRequest }>, reply: FastifyReply) => {
    try {
      const { email } = request.body;

      // Check if user exists (don't reveal if email exists or not)
      const user = await getUserService().getUserByEmail(email);

      if (user && user.is_active) {
        // Generate password reset token and send email
        const emailService = new EmailService();
        await emailService.sendPasswordReset(user.id, user.email, user.first_name);
        request.log.info(`Password reset requested for: ${email}`);
      }

      // Always return success to prevent email enumeration
      return reply.send({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });

    } catch (error) {
      request.log.error('Forgot password error:', error);
      console.error('Detailed forgot password error:', error);
      return reply.status(500).send({
        error: 'PASSWORD_RESET_FAILED',
        message: 'Password reset request failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/reset-password
   * Reset password using token
   */
  fastify.post<{ Body: ResetPasswordRequest }>('/reset-password', {
    schema: {
      body: {
        type: 'object',
        required: ['token', 'password'],
        properties: {
          token: { type: 'string' },
          password: { type: 'string', minLength: 8 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ResetPasswordRequest }>, reply: FastifyReply) => {
    try {
      const { token, password } = request.body;

      // Verify reset token
      const emailService = new EmailService();
      const tokenResult = await emailService.verifyPasswordResetToken(token);

      if (!tokenResult.success) {
        return reply.status(400).send({
          error: 'INVALID_RESET_TOKEN',
          message: tokenResult.error,
          requestId: request.id
        });
      }

      // Update user password
      const userService = getUserService();
      await userService.updatePassword(tokenResult.userId!, password);

      // Mark token as used
      await emailService.markPasswordResetTokenUsed(token);

      request.log.info(`Password reset completed for user: ${tokenResult.userId}`);

      return reply.send({
        success: true,
        message: 'Password reset successfully'
      });

    } catch (error) {
      request.log.error('Reset password error:', error);
      return reply.status(500).send({
        error: 'PASSWORD_RESET_FAILED',
        message: 'Password reset failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // EMAIL VERIFICATION
  // =============================================================================

  /**
   * POST /auth/verify-email
   * Verify email address using token
   */
  fastify.post<{ Body: EmailVerificationRequest }>('/verify-email', {
    schema: {
      body: {
        type: 'object',
        required: ['token'],
        properties: {
          token: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: EmailVerificationRequest }>, reply: FastifyReply) => {
    try {
      const { token } = request.body;

      const emailService = new EmailService();
      const result = await emailService.verifyEmailToken(token);

      if (!result.success) {
        return reply.status(400).send({
          error: 'EMAIL_VERIFICATION_FAILED',
          message: result.error,
          requestId: request.id
        });
      }

      request.log.info(`Email verified for user: ${result.userId}`);

      return reply.send({
        success: true,
        message: 'Email verified successfully'
      });

    } catch (error) {
      request.log.error('Email verification error:', error);
      return reply.status(500).send({
        error: 'EMAIL_VERIFICATION_FAILED',
        message: 'Email verification failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/resend-verification
   * Resend email verification
   */
  fastify.post<{ Body: ResendVerificationRequest }>('/resend-verification', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ResendVerificationRequest }>, reply: FastifyReply) => {
    try {
      const { email } = request.body;

      // Find user by email
      const user = await getUserService().getUserByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not for security
        return reply.send({
          success: true,
          message: 'If the email exists, a verification email has been sent'
        });
      }

      // Check if email is already verified
      if (user.email_verified) {
        return reply.status(400).send({
          error: 'EMAIL_ALREADY_VERIFIED',
          message: 'Email address is already verified',
          requestId: request.id
        });
      }

      // Send verification email
      const emailService = new EmailService();
      await emailService.sendEmailVerification(user.id, user.email, user.first_name);

      request.log.info(`Verification email resent to: ${email}`);

      return reply.send({
        success: true,
        message: 'Verification email sent successfully'
      });

    } catch (error) {
      request.log.error('Resend verification error:', error);
      console.error('Detailed resend verification error:', error);
      return reply.status(500).send({
        error: 'RESEND_VERIFICATION_FAILED',
        message: 'Resend verification failed. Please try again.',
        requestId: request.id
      });
    }
  });


}
