/**
 * Admin System Management Routes
 * 
 * Admin-only endpoints for system administration including:
 * - System settings management
 * - Health monitoring and diagnostics
 * - System statistics and metrics
 * - Configuration management
 * - Maintenance operations
 */

import { FastifyPluginAsync } from 'fastify';
import { getDatabasePool, getPoolStats, getConnectionStats } from '../../database/connection';
import { requireAuth, requireRole } from '../../middleware/jwtAuth';
import { validateEnvironment } from '../../config';

// Helper function to ensure user is authenticated admin
function ensureAdmin(request: any): { id: string; role: string } {
  if (!request.user || request.user.role !== 'ADMIN') {
    throw new Error('Admin access required');
  }
  return request.user;
}

export const adminSystemRoutes: FastifyPluginAsync = async (fastify) => {
  // Apply JWT authentication and admin role requirement to all routes
  fastify.addHook('preHandler', requireAuth);
  fastify.addHook('preHandler', requireRole('ADMIN'));

  // =============================================================================
  // SYSTEM HEALTH AND DIAGNOSTICS
  // =============================================================================

  /**
   * GET /admin/system/health
   * Get detailed system health information
   */
  fastify.get('/health', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const pool = getDatabasePool();
      const poolStats = getPoolStats();
      const connectionStats = getConnectionStats();
      const env = validateEnvironment();

      // Test database connectivity
      const dbHealthStart = Date.now();
      const dbResult = await pool.query('SELECT 1 as health_check, NOW() as timestamp');
      const dbHealthTime = Date.now() - dbHealthStart;

      // Test PostGIS
      const postGISStart = Date.now();
      const postGISResult = await pool.query('SELECT PostGIS_Version() as version');
      const postGISTime = Date.now() - postGISStart;

      // Get system metrics
      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();

      return {
        success: true,
        data: {
          timestamp: new Date().toISOString(),
          system: {
            status: 'healthy',
            uptime_seconds: uptime,
            node_version: process.version,
            platform: process.platform,
            arch: process.arch,
            environment: env.NODE_ENV,
            memory: {
              heap_used_mb: Math.round(memoryUsage.heapUsed / 1024 / 1024),
              heap_total_mb: Math.round(memoryUsage.heapTotal / 1024 / 1024),
              rss_mb: Math.round(memoryUsage.rss / 1024 / 1024),
              external_mb: Math.round(memoryUsage.external / 1024 / 1024)
            }
          },
          database: {
            status: dbResult.rows[0]?.health_check === 1 ? 'healthy' : 'unhealthy',
            response_time_ms: dbHealthTime,
            pool_stats: poolStats,
            connection_stats: connectionStats,
            server_time: dbResult.rows[0]?.timestamp
          },
          postgis: {
            status: postGISResult.rows[0]?.version ? 'healthy' : 'unhealthy',
            response_time_ms: postGISTime,
            version: postGISResult.rows[0]?.version
          }
        }
      };
    } catch (error) {
      fastify.log.error('Admin system health check failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve system health',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * GET /admin/system/metrics
   * Get comprehensive system metrics
   */
  fastify.get('/metrics', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const pool = getDatabasePool();
      
      // Get database statistics
      const dbStats = await pool.query(`
        SELECT
          schemaname,
          relname as tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples,
          n_dead_tup as dead_tuples
        FROM pg_stat_user_tables
        ORDER BY n_live_tup DESC
        LIMIT 20
      `);

      // Get table sizes
      const tableSizes = await pool.query(`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
      `);

      // Get active connections
      const connections = await pool.query(`
        SELECT 
          state,
          COUNT(*) as count
        FROM pg_stat_activity 
        WHERE datname = current_database()
        GROUP BY state
      `);

      return {
        success: true,
        data: {
          timestamp: new Date().toISOString(),
          database: {
            table_statistics: dbStats.rows,
            table_sizes: tableSizes.rows,
            active_connections: connections.rows,
            pool_stats: getPoolStats(),
            connection_stats: getConnectionStats()
          },
          system: {
            memory: process.memoryUsage(),
            uptime: process.uptime(),
            cpu_usage: process.cpuUsage(),
            version: process.version,
            platform: process.platform
          }
        }
      };
    } catch (error) {
      fastify.log.error('Admin system metrics failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve system metrics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // SYSTEM SETTINGS MANAGEMENT
  // =============================================================================

  /**
   * GET /admin/system/settings
   * Get system settings
   */
  fastify.get('/settings', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const pool = getDatabasePool();
      const result = await pool.query(`
        SELECT key, value, description, updated_at, updated_by
        FROM system_settings
        ORDER BY key
      `);

      return {
        success: true,
        data: result.rows
      };
    } catch (error) {
      fastify.log.error('Admin system settings retrieval failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve system settings',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * PATCH /admin/system/settings
   * Update system settings
   */
  fastify.patch('/settings', async (request, reply) => {
    try {
      const admin = ensureAdmin(request);
      const settings = request.body as Record<string, any>;
      
      const pool = getDatabasePool();
      const results = [];

      for (const [key, value] of Object.entries(settings)) {
        const result = await pool.query(`
          INSERT INTO system_settings (key, value, updated_by, updated_at)
          VALUES ($1, $2, $3, NOW())
          ON CONFLICT (key) 
          DO UPDATE SET 
            value = EXCLUDED.value,
            updated_by = EXCLUDED.updated_by,
            updated_at = EXCLUDED.updated_at
          RETURNING *
        `, [key, JSON.stringify(value), admin.id]);
        
        results.push(result.rows[0]);
      }

      return {
        success: true,
        data: results,
        message: 'System settings updated successfully'
      };
    } catch (error) {
      fastify.log.error('Admin system settings update failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to update system settings',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =============================================================================
  // MAINTENANCE OPERATIONS
  // =============================================================================

  /**
   * POST /admin/system/maintenance/vacuum
   * Run database vacuum operation
   */
  fastify.post('/maintenance/vacuum', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const { tables } = request.body as { tables?: string[] };
      const pool = getDatabasePool();
      
      if (tables && tables.length > 0) {
        // Vacuum specific tables
        for (const table of tables) {
          await pool.query(`VACUUM ANALYZE ${table}`);
        }
      } else {
        // Vacuum all tables
        await pool.query('VACUUM ANALYZE');
      }

      return {
        success: true,
        message: 'Database vacuum completed successfully'
      };
    } catch (error) {
      fastify.log.error('Admin database vacuum failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to run database vacuum',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * POST /admin/system/maintenance/cache-clear
   * Clear application cache
   */
  fastify.post('/maintenance/cache-clear', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const pool = getDatabasePool();
      
      // Clear expired cache entries
      const result = await pool.query(`
        DELETE FROM api_cache 
        WHERE expires_at < NOW()
      `);

      return {
        success: true,
        data: {
          cleared_entries: result.rowCount
        },
        message: 'Cache cleared successfully'
      };
    } catch (error) {
      fastify.log.error('Admin cache clear failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to clear cache',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * GET /admin/system/logs
   * Get system logs (recent entries)
   */
  fastify.get('/logs', async (request, reply) => {
    try {
      ensureAdmin(request);
      
      const query = request.query as {
        level?: 'error' | 'warn' | 'info' | 'debug';
        limit?: number;
        since?: string;
      };

      // This is a placeholder - in a real implementation, you'd integrate with your logging system
      return {
        success: true,
        data: {
          message: 'Log retrieval not implemented - integrate with your logging system',
          parameters: query
        }
      };
    } catch (error) {
      fastify.log.error('Admin log retrieval failed:', error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve logs',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
};
