/**
 * Admin Analytics Routes
 * 
 * Admin-only endpoints for system-wide usage analytics, monitoring, and management.
 * Provides comprehensive insights into system performance, user behavior, and resource utilization.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UsageAnalyticsService } from '../../services/UsageAnalyticsService';
import { requireAuth } from '../../middleware/jwtAuth';

// Request types
interface AdminAnalyticsQuery {
  startDate?: string;
  endDate?: string;
  userId?: string;
  apiKeyId?: string;
  limit?: string;
  offset?: string;
}

interface SystemMetricsQuery {
  period?: 'hour' | 'day' | 'week' | 'month';
  limit?: string;
}

export async function adminAnalyticsRoutes(fastify: FastifyInstance) {
  const analyticsService = new UsageAnalyticsService();

  // Apply JWT authentication and admin role check to all admin routes
  fastify.addHook('preHandler', requireAuth);
  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user;
    
    if (!user || user.role !== 'ADMIN') {
      return reply.code(403).send({
        success: false,
        error: 'Admin access required'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/system-overview
   * Get comprehensive system-wide analytics overview
   */
  fastify.get('/system-overview', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Get overall system stats
      const systemStats = await analyticsService.getUsageStats({});
      
      // Get today's stats
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
      const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();
      
      const todayStats = await analyticsService.getUsageStats({
        startDate: todayStart,
        endDate: todayEnd
      });

      // Get top endpoints system-wide
      const topEndpoints = await analyticsService.getTopEndpoints({}, 10);

      // Get system performance metrics
      const performanceMetrics = await analyticsService.getSystemPerformanceMetrics();

      return reply.code(200).send({
        success: true,
        data: {
          systemStats,
          todayStats,
          topEndpoints,
          performanceMetrics: performanceMetrics.slice(0, 24) // Last 24 periods
        }
      });
    } catch (error) {
      request.log.error('System overview error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch system overview'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/overview
   * Get system-wide analytics overview
   */
  fastify.get('/overview', async (request, reply) => {
    try {
      const overview = await analyticsService.getSystemOverview();

      return reply.code(200).send({
        success: true,
        data: overview
      });
    } catch (error) {
      request.log.error('Admin analytics overview error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch analytics overview'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/users
   * Get analytics for all users with pagination
   */
  fastify.get<{ Querystring: AdminAnalyticsQuery }>('/users', async (request, reply) => {
    try {
      const { limit = '50', offset = '0' } = request.query;
      
      // This would typically involve a more complex query to get user analytics
      // For now, we'll return a placeholder structure
      const userAnalytics = {
        users: [], // Would be populated with user analytics data
        totalUsers: 0,
        activeUsers: 0,
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          total: 0
        }
      };

      return reply.code(200).send({
        success: true,
        data: userAnalytics
      });
    } catch (error) {
      request.log.error('User analytics error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch user analytics'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/api-keys
   * Get analytics for all API keys
   */
  fastify.get<{ Querystring: AdminAnalyticsQuery }>('/api-keys', async (request, reply) => {
    try {
      const { limit = '50', offset = '0', startDate, endDate } = request.query;
      
      // Get API key usage statistics
      const apiKeyStats = await analyticsService.getUsageStats({
        startDate,
        endDate
      });

      return reply.code(200).send({
        success: true,
        data: {
          stats: apiKeyStats,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset)
          }
        }
      });
    } catch (error) {
      request.log.error('API key analytics error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch API key analytics'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/system-metrics
   * Get detailed system performance metrics
   */
  fastify.get<{ Querystring: SystemMetricsQuery }>('/system-metrics', async (request, reply) => {
    try {
      const { period = 'day', limit = '30' } = request.query;
      
      const metrics = await analyticsService.getSystemPerformanceMetrics();
      
      return reply.code(200).send({
        success: true,
        data: {
          metrics: metrics.slice(0, parseInt(limit)),
          period
        }
      });
    } catch (error) {
      request.log.error('System metrics error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch system metrics'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/alerts
   * Get all system alerts
   */
  fastify.get<{ Querystring: { includeResolved?: string; severity?: string } }>('/alerts', async (request, reply) => {
    try {
      const { includeResolved = 'false', severity } = request.query;

      // This would get all alerts across all users
      // For now, return empty array as placeholder
      const alerts: any[] = [];

      // Use the parameters to avoid unused variable warnings
      console.log(`Getting alerts with includeResolved: ${includeResolved}, severity: ${severity || 'all'}`);

      return reply.code(200).send({
        success: true,
        data: alerts
      });
    } catch (error) {
      request.log.error('System alerts error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch system alerts'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/usage-trends
   * Get system-wide usage trends
   */
  fastify.get<{ Querystring: AdminAnalyticsQuery }>('/usage-trends', async (request, reply) => {
    try {
      const { startDate, endDate } = request.query;
      
      const trends = await analyticsService.getTimeSeriesData({
        startDate,
        endDate
      }, 'day');

      return reply.code(200).send({
        success: true,
        data: trends
      });
    } catch (error) {
      request.log.error('Usage trends error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch usage trends'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/user/:userId
   * Get detailed analytics for a specific user
   */
  fastify.get<{ Params: { userId: string }; Querystring: AdminAnalyticsQuery }>('/user/:userId', async (request, reply) => {
    try {
      const { userId } = request.params;
      const { startDate, endDate } = request.query;
      
      const [userStats, userQuotas, userAlerts, userTopEndpoints] = await Promise.all([
        analyticsService.getUsageStats({ userId, startDate, endDate }),
        analyticsService.getUsageQuotas(userId),
        analyticsService.getUsageAlerts(userId, true),
        analyticsService.getTopEndpoints({ userId, startDate, endDate }, 10)
      ]);

      return reply.code(200).send({
        success: true,
        data: {
          userId,
          stats: userStats,
          quotas: userQuotas,
          alerts: userAlerts,
          topEndpoints: userTopEndpoints
        }
      });
    } catch (error) {
      request.log.error('User analytics error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to fetch user analytics'
      });
    }
  });

  /**
   * POST /api/v1/admin/analytics/alerts/:alertId/resolve
   * Resolve any system alert (admin override)
   */
  fastify.post<{ Params: { alertId: string } }>('/alerts/:alertId/resolve', async (request, reply) => {
    try {
      const { alertId } = request.params;
      
      await analyticsService.resolveUsageAlert(alertId);
      
      return reply.code(200).send({
        success: true,
        message: 'Alert resolved successfully'
      });
    } catch (error) {
      request.log.error('Resolve alert error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to resolve alert'
      });
    }
  });

  /**
   * GET /api/v1/admin/analytics/export
   * Export analytics data (CSV/JSON)
   */
  fastify.get<{ 
    Querystring: AdminAnalyticsQuery & { 
      format?: 'csv' | 'json';
      type?: 'usage' | 'users' | 'api-keys' | 'alerts';
    } 
  }>('/export', async (request, reply) => {
    try {
      const { format = 'json', type = 'usage', startDate, endDate } = request.query;
      
      let data: any;
      
      switch (type) {
        case 'usage':
          data = await analyticsService.getUsageStats({ startDate, endDate });
          break;
        case 'users':
          // Would implement user export
          data = { message: 'User export not implemented yet' };
          break;
        case 'api-keys':
          // Would implement API key export
          data = { message: 'API key export not implemented yet' };
          break;
        case 'alerts':
          // Would implement alerts export
          data = { message: 'Alerts export not implemented yet' };
          break;
        default:
          return reply.code(400).send({
            success: false,
            error: 'Invalid export type'
          });
      }

      if (format === 'csv') {
        // Would implement CSV conversion
        reply.header('Content-Type', 'text/csv');
        reply.header('Content-Disposition', `attachment; filename="${type}-export.csv"`);
        return reply.send('CSV export not implemented yet');
      }

      return reply.code(200).send({
        success: true,
        data,
        exportInfo: {
          type,
          format,
          generatedAt: new Date().toISOString()
        }
      });
    } catch (error) {
      request.log.error('Export error:', error);
      return reply.code(500).send({
        success: false,
        error: 'Failed to export data'
      });
    }
  });
}
