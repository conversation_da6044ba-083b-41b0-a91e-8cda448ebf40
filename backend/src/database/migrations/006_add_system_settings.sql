-- Migration: Add system settings table
-- Description: Create table for storing system-wide configuration settings

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
    key VARCHAR(255) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_system_settings_updated_at ON system_settings(updated_at);

-- Insert default system settings
INSERT INTO system_settings (key, value, description) VALUES
    ('maintenance_mode', 'false', 'Enable/disable system maintenance mode'),
    ('api_rate_limits', '{"default_per_minute": 60, "default_per_day": 10000, "burst_limit": 100}', 'Default API rate limiting configuration'),
    ('email_settings', '{"smtp_enabled": true, "from_email": "<EMAIL>", "from_name": "Postal Terminal API"}', 'Email system configuration'),
    ('security_settings', '{"password_min_length": 8, "require_email_verification": true, "session_timeout_minutes": 1440}', 'Security policy settings')
ON CONFLICT (key) DO NOTHING;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_system_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_system_settings_updated_at();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON system_settings TO postgres;
