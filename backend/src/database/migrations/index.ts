import { readFileSync } from 'fs';
import { join } from 'path';
import { getDatabasePool } from '../connection';

export interface Migration {
  id: string;
  name: string;
  description: string;
  sql: string;
  created: Date;
}

// Migration tracking table
const MIGRATIONS_TABLE_SQL = `
CREATE TABLE IF NOT EXISTS migrations (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  checksum VARCHAR(64) NOT NULL
);
`;

// Available migrations in order
const MIGRATIONS: Omit<Migration, 'sql' | 'created'>[] = [
  {
    id: '000_complete_schema',
    name: 'Complete Database Schema',
    description: 'Complete postal terminal API database setup with PostGIS, all tables, indexes, functions, and verification'
  },
  {
    id: '001_add_tracking_tables',
    name: 'Add Tracking Tables',
    description: 'Adds tracking_snapshots and tracking_events tables for shipment tracking data'
  },
  {
    id: '002_add_saas_authentication',
    name: 'SaaS Authentication System',
    description: 'Core authentication system with users, JWT tokens, OAuth support, and enhanced API key management'
  },
  {
    id: '003_add_subscription_management',
    name: 'Subscription Management System',
    description: 'Complete subscription management with plans, user subscriptions, orders, and audit logging for SaaS billing'
  },
  {
    id: '004_add_usage_analytics',
    name: 'Usage Analytics System',
    description: 'Comprehensive usage tracking, analytics, monitoring, and quota management for API usage'
  },
  {
    id: '005_email_verification_tables',
    name: 'Email Verification and Password Reset Tables',
    description: 'Email verification and password reset tables for user account management'
  },
  {
    id: '006_add_system_settings',
    name: 'System Settings Table',
    description: 'System settings table for configuring API rate limits, email settings, security policies, and more'
  }
];

// Load migration SQL from file
function loadMigrationSQL(migrationId: string): string {
  const filePath = join(__dirname, `${migrationId}.sql`);
  return readFileSync(filePath, 'utf-8');
}

// Calculate checksum for migration content
function calculateChecksum(content: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(content).digest('hex');
}

// Check if migration has been executed
async function isMigrationExecuted(migrationId: string): Promise<boolean> {
  const pool = getDatabasePool();
  const result = await pool.query(
    'SELECT id FROM migrations WHERE id = $1',
    [migrationId]
  );
  return result.rows.length > 0;
}

// Record migration execution
async function recordMigration(migration: Migration): Promise<void> {
  const pool = getDatabasePool();
  const checksum = calculateChecksum(migration.sql);
  
  await pool.query(`
    INSERT INTO migrations (id, name, description, checksum)
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (id) DO UPDATE SET
      executed_at = NOW(),
      checksum = EXCLUDED.checksum
  `, [migration.id, migration.name, migration.description, checksum]);
}

// Execute a single migration
async function executeMigration(migration: Migration): Promise<void> {
  const pool = getDatabasePool();
  
  console.log(`🔄 Executing migration: ${migration.id} - ${migration.name}`);
  
  try {
    // Execute migration SQL
    await pool.query(migration.sql);
    
    // Record successful execution
    await recordMigration(migration);
    
    console.log(`✅ Migration completed: ${migration.id}`);
  } catch (error) {
    console.error(`❌ Migration failed: ${migration.id}`, error);
    throw error;
  }
}

// Run all pending migrations
export async function runMigrations(): Promise<void> {
  const pool = getDatabasePool();
  
  console.log('🚀 Starting database migrations...');
  
  try {
    // Ensure migrations table exists
    await pool.query(MIGRATIONS_TABLE_SQL);
    
    // Process each migration
    for (const migrationInfo of MIGRATIONS) {
      const isExecuted = await isMigrationExecuted(migrationInfo.id);
      
      if (!isExecuted) {
        const sql = loadMigrationSQL(migrationInfo.id);
        const migration: Migration = {
          ...migrationInfo,
          sql,
          created: new Date()
        };
        
        await executeMigration(migration);
      } else {
        console.log(`⏭️  Skipping already executed migration: ${migrationInfo.id}`);
      }
    }
    
    console.log('🎉 All migrations completed successfully!');
  } catch (error) {
    console.error('💥 Migration process failed:', error);
    throw error;
  }
}

// Get migration status
export async function getMigrationStatus(): Promise<{ executed: string[]; pending: string[] }> {
  const pool = getDatabasePool();
  
  // Ensure migrations table exists
  await pool.query(MIGRATIONS_TABLE_SQL);
  
  const result = await pool.query('SELECT id FROM migrations ORDER BY executed_at');
  const executed = result.rows.map(row => row.id);
  const pending = MIGRATIONS
    .map(m => m.id)
    .filter(id => !executed.includes(id));
  
  return { executed, pending };
}

// Reset database - drops all tables and re-runs migrations
export async function resetDatabase(): Promise<void> {
  const pool = getDatabasePool();
  
  console.log('🔥 Resetting database - dropping all tables...');
  
  try {
    // Drop all tables in the correct order (respecting foreign keys)
    const dropTablesSQL = `
      -- Drop tables if they exist
      DROP TABLE IF EXISTS usage_analytics CASCADE;
      DROP TABLE IF EXISTS api_cache CASCADE;
      DROP TABLE IF EXISTS api_keys CASCADE;
      DROP TABLE IF EXISTS terminals CASCADE;
      DROP TABLE IF EXISTS tenants CASCADE;
      DROP TABLE IF EXISTS tracking_events CASCADE;
      DROP TABLE IF EXISTS tracking_snapshots CASCADE;
      DROP TABLE IF EXISTS migrations CASCADE;
      
      -- Drop functions if they exist
      DROP FUNCTION IF EXISTS update_search_vector() CASCADE;
      DROP FUNCTION IF EXISTS clean_expired_cache() CASCADE;
      DROP FUNCTION IF EXISTS update_tenant_timestamp() CASCADE;
      DROP FUNCTION IF EXISTS increment_api_key_usage(UUID, BOOLEAN, BOOLEAN) CASCADE;
      DROP FUNCTION IF EXISTS reset_monthly_usage() CASCADE;
      DROP FUNCTION IF EXISTS get_terminal_distance(GEOGRAPHY, DOUBLE PRECISION, DOUBLE PRECISION) CASCADE;
      DROP FUNCTION IF EXISTS standardize_postal_code(TEXT, TEXT) CASCADE;
      
      -- Note: We don't drop extensions as they might be used by other databases
      -- Extensions (postgis, pg_trgm, uuid-ossp) will remain available
    `;
    
    await pool.query(dropTablesSQL);
    console.log('✅ All tables and functions dropped');
    
    // Now run migrations fresh
    console.log('🔄 Running fresh migrations...');
    await runMigrations();
    
    console.log('🎉 Database reset completed successfully!');
  } catch (error) {
    console.error('💥 Database reset failed:', error);
    throw error;
  }
}

// Rollback last migration (basic implementation)
export async function rollbackLastMigration(): Promise<void> {
  const pool = getDatabasePool();
  
  const result = await pool.query(`
    SELECT id FROM migrations 
    ORDER BY executed_at DESC 
    LIMIT 1
  `);
  
  if (result.rows.length === 0) {
    console.log('No migrations to rollback');
    return;
  }
  
  const lastMigrationId = result.rows[0].id;
  console.log(`⚠️  Rolling back migration: ${lastMigrationId}`);
  
  // Remove from migrations table
  await pool.query('DELETE FROM migrations WHERE id = $1', [lastMigrationId]);
  
  console.log(`✅ Rollback completed for: ${lastMigrationId}`);
  console.log('⚠️  Note: This only removes the migration record. Manual cleanup may be required.');
}
