# API Documentation for Integration - Postal Terminal API

## Overview
This document provides comprehensive integration documentation for the Postal Terminal API, including tested request/response formats, authentication methods, and practical examples.

## Base URL
- **Development**: `http://localhost:3000`
- **Production**: TBD

## Authentication

### API Key Authentication
All protected endpoints require an API key in one of these formats:

**Option 1: Authorization Header (Recommended)**
```bash
Authorization: Bearer ptapi_your_api_key_here
```

**Option 2: Custom Header (Legacy)**
```bash
X-API-Key: ptapi_your_api_key_here
```

### JWT Authentication (SaaS Features)
For user management and subscription features:
```bash
Authorization: Bearer jwt_token_here
```

---

## Core Terminal Data Endpoints

### 1. Health Check ✅ TESTED
**Endpoint**: `GET /api/v1/health`  
**Authentication**: None required  
**Purpose**: System health monitoring

**Request Example**:
```bash
curl -s http://localhost:3000/api/v1/health
```

**Response Example**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-13T05:53:49.798Z",
  "version": "2.1.0",
  "checks": {
    "database": "healthy",
    "cache": "healthy",
    "apiKeys": "healthy"
  }
}
```

**Response Fields**:
- `status`: `"healthy"` | `"degraded"` | `"unhealthy"`
- `timestamp`: ISO 8601 timestamp
- `version`: API version
- `checks`: Health status of individual components

---

### 2. API Information ✅ TESTED
**Endpoint**: `GET /api/v1/info`  
**Authentication**: API Key required  
**Purpose**: Get API metadata and available endpoints

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  http://localhost:3000/api/v1/info
```

**Response Example**:
```json
{
  "name": "Postal Terminal API",
  "version": "2.1.0",
  "environment": "development",
  "status": "operational",
  "timestamp": "2025-07-13T05:55:43.316Z",
  "endpoints": {
    "health": {
      "path": "/api/v1/health",
      "method": "GET",
      "auth": false,
      "description": "System health check"
    },
    "terminals": {
      "path": "/api/v1/terminals",
      "method": "GET",
      "auth": true,
      "description": "List postal terminals with filtering"
    }
  },
  "authentication": {
    "type": "API Key",
    "header": "Authorization: Bearer <api_key>",
    "required": "All endpoints except /health and /"
  },
  "rateLimiting": {
    "default": "1000 requests per minute",
    "root": "100 requests per minute"
  }
}
```

---

### 3. List Terminals ✅ TESTED
**Endpoint**: `GET /api/v1/terminals`  
**Authentication**: API Key required  
**Purpose**: List postal terminals with filtering and pagination

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Results per page
- `sortBy` (string, default: "name"): Sort field (`name`, `city`, `provider`, `updated`)
- `sortOrder` (string, default: "asc"): Sort direction (`asc`, `desc`)
- `city` (string): Filter by city name
- `provider` (string): Filter by provider (`LP_EXPRESS`, `OMNIVA`, `DPD`, `VENIPAK`)
- `terminalType` (string): Filter by type (`PARCEL_LOCKER`, `PICKUP_POINT`, `POST_OFFICE`)
- `isActive` (boolean, default: true): Filter by active status

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  "http://localhost:3000/api/v1/terminals?limit=5&city=Vilnius"
```

**Response Example**:
```json
{
  "data": [
    {
      "id": "lp_4504",
      "name": "Aibė",
      "city": "Vėžaičiai",
      "address": "Gargždų g.29, Vėžaičiai",
      "postalCode": "96216",
      "coordinates": {
        "lat": 55.7181,
        "lng": 21.4754
      },
      "updated": "2025-07-09T07:20:20.646Z",
      "provider": "LP_EXPRESS",
      "terminalType": "PARCEL_LOCKER",
      "isActive": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 5,
    "total": 1923,
    "totalPages": 385,
    "hasNext": true,
    "hasPrev": false
  },
  "meta": {
    "requestId": "generated-request-id",
    "timestamp": "2025-07-13T05:55:50.699Z",
    "processingTime": 19
  }
}
```

**Response Fields**:
- `data`: Array of terminal objects
- `pagination`: Pagination metadata
- `meta`: Request metadata including processing time

---

### 4. Get Terminal by ID ✅ TESTED
**Endpoint**: `GET /api/v1/terminals/:id`  
**Authentication**: API Key required  
**Purpose**: Get detailed information for a specific terminal

**Path Parameters**:
- `id` (string): Terminal ID (e.g., "lp_4504")

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  http://localhost:3000/api/v1/terminals/lp_4504
```

**Response Example**:
```json
{
  "id": "lp_4504",
  "name": "Aibė",
  "city": "Vėžaičiai",
  "address": "Gargždų g.29, Vėžaičiai",
  "postalCode": "96216",
  "coordinates": {
    "lat": 55.7181,
    "lng": 21.4754
  },
  "updated": "2025-07-09T07:20:20.646Z",
  "provider": "LP_EXPRESS",
  "terminalType": "PARCEL_LOCKER",
  "isActive": true
}
```

---

### 5. Search Terminals ✅ TESTED
**Endpoint**: `GET /api/v1/terminals/search`  
**Authentication**: API Key required  
**Purpose**: Full-text search across terminal names, cities, addresses, and postal codes

**Query Parameters**:
- `q` (string, required): Search query
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20): Results per page
- `sortBy` (string, default: "name"): Sort field
- `sortOrder` (string, default: "asc"): Sort direction
- Additional filters: `city`, `provider`, `terminalType`, `isActive`

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  "http://localhost:3000/api/v1/terminals/search?q=Vilnius&limit=3"
```

**Response Example**:
```json
{
  "data": [
    {
      "id": "lp_1503",
      "name": "Aibė",
      "city": "Mickūnai",
      "address": "Vilniaus g. 17A",
      "postalCode": "13116",
      "coordinates": {
        "lat": 54.702425,
        "lng": 25.520959
      },
      "updated": "2025-07-09T07:20:20.646Z",
      "provider": "LP_EXPRESS",
      "terminalType": "PARCEL_LOCKER",
      "isActive": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 3,
    "total": 554,
    "totalPages": 185,
    "hasNext": true,
    "hasPrev": false
  },
  "meta": {
    "requestId": "generated-request-id",
    "timestamp": "2025-07-13T05:56:07.910Z",
    "processingTime": 52
  }
}
```

---

### 6. Find Nearby Terminals ✅ TESTED
**Endpoint**: `GET /api/v1/terminals/nearby`  
**Authentication**: API Key required  
**Purpose**: Find terminals within a specified radius of coordinates

**Query Parameters**:
- `lat` (number, required): Latitude coordinate
- `lng` (number, required): Longitude coordinate
- `radius` (number, default: 10, max: 100): Search radius in kilometers
- `limit` (integer, default: 20): Maximum results
- `provider` (string): Filter by provider
- `terminalType` (string): Filter by terminal type

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  "http://localhost:3000/api/v1/terminals/nearby?lat=54.6872&lng=25.2797&radius=2&limit=3"
```

**Response Example**:
```json
{
  "data": [
    {
      "id": "dpd_LT90405",
      "name": "Gedimino Maxima DPD paštom 405",
      "city": "Vilnius",
      "address": "Gedimino pr. 18",
      "postalCode": "01102",
      "coordinates": {
        "lat": 54.6868962,
        "lng": 25.2792253
      },
      "updated": "2025-07-09T07:20:20.821Z",
      "provider": "DPD",
      "terminalType": "PARCEL_LOCKER",
      "isActive": true,
      "distance": 0.04561684443
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 3,
    "total": 78,
    "totalPages": 26,
    "hasNext": true,
    "hasPrev": false
  },
  "meta": {
    "requestId": "generated-request-id",
    "timestamp": "2025-07-13T05:56:15.352Z",
    "processingTime": 51
  }
}
```

**Note**: Results include a `distance` field showing distance in kilometers from the search coordinates.

---

### 7. Package Tracking ✅ TESTED
**Endpoint**: `GET /api/v1/track`  
**Authentication**: API Key required  
**Purpose**: Track packages across multiple providers

**Query Parameters**:
- `provider` (string, required): Provider code (`LP_EXPRESS`, `OMNIVA`, `DPD`, `VENIPAK`)
- `trackingNumber` (string, required): Package tracking number
- `refresh` (boolean, default: false): Force refresh from provider

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  "http://localhost:3000/api/v1/track?provider=LP_EXPRESS&trackingNumber=LP12345678"
```

**Response Example**:
```json
{
  "data": {
    "provider": "LP_EXPRESS",
    "trackingNumber": "LP12345678",
    "status": "EXCEPTION",
    "statusText": {
      "en": "Unknown",
      "lt": "Nežinoma",
      "original": "UNKNOWN"
    },
    "events": [],
    "lastUpdated": "2025-07-13T05:56:23.250Z"
  },
  "meta": {
    "requestId": "req_542367ce6f70b9b0",
    "cacheHit": false,
    "responseTime": 651
  }
}
```

**Status Values**:
- `PENDING`: Package registered but not yet in transit
- `IN_TRANSIT`: Package is being transported
- `OUT_FOR_DELIVERY`: Package is out for delivery
- `DELIVERED`: Package has been delivered
- `RETURNED`: Package returned to sender
- `EXCEPTION`: Error or unknown status

---

### 8. System Metrics ✅ TESTED
**Endpoint**: `GET /api/v1/metrics`  
**Authentication**: API Key required  
**Purpose**: Get system performance metrics

**Request Example**:
```bash
curl -s -H "Authorization: Bearer ptapi_your_key" \
  http://localhost:3000/api/v1/metrics
```

**Response Example**:
```json
{
  "system": {
    "health": 1,
    "uptime_seconds": 205,
    "memory": {
      "heap_used_bytes": 31177360,
      "heap_total_bytes": 34324480,
      "rss_bytes": 143278080,
      "external_bytes": 4053732
    },
    "node_version": "v22.16.0",
    "platform": "darwin",
    "arch": "arm64"
  },
  "database": {
    "connections": {
      "total": 1,
      "idle": 1,
      "waiting": 0
    },
    "stats": {
      "created_total": 1,
      "acquired_total": 64,
      "released_total": 64,
      "errors_total": 0,
      "last_activity": "2025-07-13T05:56:31.245Z"
    }
  },
  "meta": {
    "timestamp": "2025-07-13T05:56:31.245Z",
    "format": "json"
  }
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": [],
    "requestId": "uuid",
    "timestamp": "2025-07-13T05:56:31.245Z"
  }
}
```

### Common Error Codes
- `MISSING_API_KEY`: API key not provided
- `INVALID_API_KEY`: API key is invalid or inactive
- `RATE_LIMIT_EXCEEDED`: Rate limit exceeded
- `VALIDATION_ERROR`: Request validation failed
- `NOT_FOUND`: Resource not found
- `INTERNAL_ERROR`: Server error

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (validation error)
- `401`: Unauthorized (missing/invalid API key)
- `404`: Not Found
- `429`: Too Many Requests (rate limited)
- `500`: Internal Server Error

---

## Rate Limiting

### Default Limits
- **API Key endpoints**: 1000 requests per minute
- **Root endpoint**: 100 requests per minute
- **Burst limit**: 2000 requests (configurable per key)

### Rate Limit Headers
Response headers include rate limiting information:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

---

## Performance Characteristics

### Response Times (Tested)
- Health check: ~5ms
- Terminal list: ~19ms
- Terminal by ID: ~10ms (cached)
- Search: ~52ms
- Nearby search: ~51ms
- Package tracking: ~651ms (external API call)
- System metrics: ~15ms

### Caching
- Terminal data: 5-10 minutes TTL
- Search results: 5 minutes TTL
- Package tracking: 5 minutes TTL (active), 24 hours (delivered)

---

## Integration Best Practices

### 1. Authentication
- Always use the `Authorization: Bearer` header format
- Store API keys securely (environment variables, secrets management)
- Rotate API keys regularly

### 2. Error Handling
- Always check HTTP status codes
- Parse error responses for detailed error information
- Implement exponential backoff for rate limit errors

### 3. Performance
- Use pagination for large result sets
- Implement client-side caching where appropriate
- Monitor response times and adjust timeouts accordingly

### 4. Rate Limiting
- Respect rate limits and implement proper backoff
- Use burst capacity wisely for peak loads
- Monitor rate limit headers in responses

---

## Next Steps

This document will be expanded with:
- JWT authentication endpoints testing
- SaaS user management endpoints
- Subscription management endpoints
- Admin endpoints testing
- Webhook endpoints testing
- Complete error scenario testing
